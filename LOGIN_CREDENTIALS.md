# TrakSong System Login Credentials

This document contains all the login credentials for testing the TrakSong system. These credentials are created by the seed script (`api/scripts/seed.js`).

## Admin Account

**Username:** `admin`  
**Password:** `admin123`  
**Email:** `<EMAIL>`  
**Role:** Administrator  
**Access:** Full system access including user management, analytics, compliance, and all administrative functions.

## Store Accounts

Store accounts are created with the pattern `store{number}` and password `store{number}123`:

1. **Username:** `store1` | **Password:** `store1123` | **Email:** `<EMAIL>`
2. **Username:** `store2` | **Password:** `store2123` | **Email:** `<EMAIL>`
3. **Username:** `store3` | **Password:** `store3123` | **Email:** `<EMAIL>`
4. **Username:** `store4` | **Password:** `store4123` | **Email:** `<EMAIL>`
5. **Username:** `store5` | **Password:** `store5123` | **Email:** `<EMAIL>`

## Artist Accounts

Artist accounts are created with the pattern `artist{number}` and password `artist123`:

### 1. Kabza De Small
**Username:** `artist1`  
**Password:** `artist123`  
**Email:** `<EMAIL>`  
**Artist Name:** Kabza De Small  
**Stage Name:** Kabza De Small  
**Real Name:** Kabelo Motha  
**Genre:** Amapiano, House, Deep House  
**City:** Johannesburg  
**SAMRO Member:** SAM001  

### 2. DJ Maphorisa
**Username:** `artist2`  
**Password:** `artist123`  
**Email:** `<EMAIL>`  
**Artist Name:** DJ Maphorisa  
**Stage Name:** DJ Maphorisa  
**Real Name:** Themba Sekowe  
**Genre:** Amapiano, Gqom, Afrobeats, House  
**City:** Johannesburg  
**SAMRO Member:** SAM002  

### 3. Black Coffee
**Username:** `artist3`  
**Password:** `artist123`  
**Email:** `<EMAIL>`  
**Artist Name:** Black Coffee  
**Stage Name:** Black Coffee  
**Real Name:** Nkosinathi Innocent Maphumulo  
**Genre:** Deep House, Afro House, Tech House  
**City:** Durban  
**SAMRO Member:** SAM003  

### 4. Focalistic
**Username:** `artist4`  
**Password:** `artist123`  
**Email:** `<EMAIL>`  
**Artist Name:** Focalistic  
**Stage Name:** Focalistic  
**Real Name:** Lethabo Sebetso  
**Genre:** Amapiano, Hip Hop, Rap  
**City:** Pretoria  
**SAMRO Member:** SAM004  

### 5. Sha Sha
**Username:** `artist5`  
**Password:** `artist123`  
**Email:** `<EMAIL>`  
**Artist Name:** Sha Sha  
**Stage Name:** Sha Sha  
**Real Name:** Charmaine Shamiso Mapimbiro  
**Genre:** Amapiano, Afro Pop, R&B  
**City:** Johannesburg  
**SAMRO Member:** SAM005  

## Label Accounts

Label accounts are created with the pattern `label{number}` and password `label123`:

### 1. Piano Hub Records
**Username:** `label1`  
**Password:** `label123`  
**Email:** `<EMAIL>`  
**Label Name:** Piano Hub Records  
**Display Name:** Piano Hub  
**Company Type:** Independent  
**Founded:** 2018  
**Genres:** Amapiano, Deep House, Afro House  
**Location:** Johannesburg, Gauteng  
**Website:** https://pianohub.co.za  

### 2. Blaq Boy Music
**Username:** `label2`  
**Password:** `label123`  
**Email:** `<EMAIL>`  
**Label Name:** Blaq Boy Music  
**Display Name:** Blaq Boy Music  
**Company Type:** Independent  
**Founded:** 2016  
**Genres:** Amapiano, Gqom, Afrobeats, House  
**Location:** Johannesburg, Gauteng  
**Website:** https://blaqboymusic.com  
**Owner:** Themba Sekowe (DJ Maphorisa)  

### 3. Soulistic Music
**Username:** `label3`  
**Password:** `label123`  
**Email:** `<EMAIL>`  
**Label Name:** Soulistic Music  
**Display Name:** Soulistic Music  
**Company Type:** Independent  
**Founded:** 2005  
**Genres:** Deep House, Afro House, Tech House, Progressive House  
**Location:** Cape Town, Western Cape  
**Website:** https://soulisticmusic.com  
**Home to:** Black Coffee and other house music artists  

## Compliance Users

Compliance users are created for SAMRO and SAMPRA organizations:

### SAMRO Staff
**Username:** `samro_staff1`  
**Password:** `samro123`  
**Email:** `<EMAIL>`  
**Organization:** SAMRO  
**Role:** SAMRO Staff  

### SAMPRA Staff
**Username:** `sampra_staff1`  
**Password:** `sampra123`  
**Email:** `<EMAIL>`  
**Organization:** SAMPRA  
**Role:** SAMPRA Staff  

## Usage Notes

1. **Artist Dashboard Access:** Use artist credentials to access the artist dashboard for music catalogue management, analytics, and track uploads.

2. **Label Dashboard Access:** Use label credentials to access the label dashboard for managing artists, catalogue, and analytics.

3. **Store Dashboard Access:** Use store credentials to access the store-specific dashboard for playlist management and compliance tracking.

4. **Admin Dashboard Access:** Use admin credentials for full system administration, user management, and system-wide analytics.

5. **Compliance Access:** Use SAMRO/SAMPRA staff credentials for compliance monitoring and reporting.

## Database Seeding

To populate the database with this test data, run:
```bash
cd api
npm run seed
```

This will create all the users, stores, artists, labels, and sample tracks with the credentials listed above.
