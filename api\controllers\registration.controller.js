const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
const Store = require('../models/Store.model');
const User = require('../models/User.model');

const EmailVerification = require('../models/EmailVerification.model');
const VenueLicense = require('../models/VenueLicense.model');
const emailService = require('../services/email.service');

// Register a new store with email verification
exports.registerStore = async (req, res) => {
  try {
    const {
      // Store details
      storeName,
      storeEmail,
      storeAddress,
      storeCity,
      storeProvince,
      storePostalCode,
      storePhone,
      businessType,
      businessRegistrationNumber,
      vatNumber,
      // License details
      samroLicenseNumber,
      samroTariffCode,
      samroExpiryDate,
      sampraLicenseNumber,
      sampraTariffCode,
      sampraExpiryDate,
      risaRegistrationNumber,
      // Owner details
      ownerName,
      ownerUsername,
      ownerPassword
    } = req.body;

    // Validation
    if (!storeName || !storeEmail || !ownerName || !ownerUsername || !ownerPassword || !businessType) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['storeName', 'storeEmail', 'ownerName', 'ownerUsername', 'ownerPassword', 'businessType']
      });
    }

    // Check if email is already registered
    const existingStore = await Store.findOne({ email: storeEmail.toLowerCase() });
    if (existingStore) {
      return res.status(400).json({ error: 'A store with this email is already registered' });
    }

    // Check if username is already taken
    const existingUser = await User.findOne({ username: ownerUsername });
    if (existingUser) {
      return res.status(400).json({ error: 'Username is already taken' });
    }

    // Check if user email is already registered
    const existingUserEmail = await User.findOne({ email: storeEmail.toLowerCase() });
    if (existingUserEmail) {
      return res.status(400).json({ error: 'A user with this email is already registered' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(ownerPassword, 12);

    // Generate verification code
    const verificationCode = EmailVerification.generateCode();

    // Create email verification record
    const emailVerification = await EmailVerification.create({
      email: storeEmail.toLowerCase(),
      verificationCode,
      type: 'store_registration',
      registrationData: {
        storeName,
        storeAddress,
        storeCity,
        storeProvince,
        storePostalCode,
        storePhone,
        businessType,
        businessRegistrationNumber,
        vatNumber,
        // License information
        samroLicenseNumber,
        samroTariffCode,
        samroExpiryDate: samroExpiryDate ? new Date(samroExpiryDate) : null,
        sampraLicenseNumber,
        sampraTariffCode,
        sampraExpiryDate: sampraExpiryDate ? new Date(sampraExpiryDate) : null,
        risaRegistrationNumber,
        ownerName,
        ownerUsername,
        ownerPassword: hashedPassword
      },
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent')
    });

    // Send verification email
    const emailResult = await emailService.sendVerificationEmail(
      storeEmail,
      verificationCode,
      storeName,
      ownerName
    );

    if (!emailResult.success) {
      // Clean up verification record if email failed
      await EmailVerification.findByIdAndDelete(emailVerification._id);
      return res.status(500).json({ 
        error: 'Failed to send verification email. Please try again.' 
      });
    }

    res.status(200).json({
      message: 'Registration initiated successfully. Please check your email for the verification code.',
      email: storeEmail,
      expiresAt: emailVerification.expiresAt
    });

  } catch (error) {
    console.error('Store registration error:', error);
    res.status(500).json({ 
      error: 'Registration failed. Please try again.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Verify email and complete store registration
exports.verifyStoreRegistration = async (req, res) => {
  try {
    const { email, verificationCode } = req.body;

    if (!email || !verificationCode) {
      return res.status(400).json({ error: 'Email and verification code are required' });
    }

    // Find verification record
    const verification = await EmailVerification.findOne({
      email: email.toLowerCase(),
      type: 'store_registration',
      verificationCode
    });

    if (!verification) {
      return res.status(400).json({ error: 'Invalid verification code' });
    }

    // Check if verification is valid
    if (!verification.isValid()) {
      return res.status(400).json({ 
        error: verification.isUsed ? 'Verification code has already been used' :
               verification.expiresAt < new Date() ? 'Verification code has expired' :
               'Too many verification attempts. Please request a new code.'
      });
    }

    // Start transaction for atomic operation
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { registrationData } = verification;

      // Create store
      const store = await Store.create([{
        name: registrationData.storeName,
        email: email.toLowerCase(),
        address: registrationData.storeAddress,
        city: registrationData.storeCity,
        province: registrationData.storeProvince,
        postalCode: registrationData.storePostalCode,
        phone: registrationData.storePhone,
        businessType: registrationData.businessType,
        businessRegistrationNumber: registrationData.businessRegistrationNumber,
        vatNumber: registrationData.vatNumber,
        // License information
        licenses: {
          samro: {
            licenseNumber: registrationData.samroLicenseNumber || '',
            tariffCode: registrationData.samroTariffCode || '',
            expiryDate: registrationData.samroExpiryDate,
            isActive: !!registrationData.samroLicenseNumber
          },
          sampra: {
            licenseNumber: registrationData.sampraLicenseNumber || '',
            tariffCode: registrationData.sampraTariffCode || '',
            expiryDate: registrationData.sampraExpiryDate,
            isActive: !!registrationData.sampraLicenseNumber
          },
          risa: {
            registrationNumber: registrationData.risaRegistrationNumber || '',
            isCompliant: true
          }
        },
        verificationStatus: 'verified',
        verifiedAt: new Date()
      }], { session });

      // Create user
      const user = await User.create([{
        username: registrationData.ownerUsername,
        email: email.toLowerCase(),
        password: registrationData.ownerPassword,
        role: 'store',
        storeId: store[0]._id,
        emailVerified: true,
        accountStatus: 'active'
      }], { session });

      // Update store with user reference
      await Store.findByIdAndUpdate(
        store[0]._id,
        { $push: { users: user[0]._id } },
        { session }
      );

      // Create VenueLicense record if license information provided
      if (registrationData.samroLicenseNumber || registrationData.sampraLicenseNumber) {
        const licenseId = `VL-${Date.now()}-${store[0]._id.toString().slice(-6)}`;

        await VenueLicense.create([{
          licenseId,
          venue: {
            storeId: store[0]._id,
            name: registrationData.storeName,
            address: {
              street: registrationData.storeAddress,
              city: registrationData.storeCity,
              province: registrationData.storeProvince,
              postalCode: registrationData.storePostalCode,
              country: 'South Africa'
            },
            venueType: registrationData.businessType
          },
          licenseType: registrationData.samroLicenseNumber && registrationData.sampraLicenseNumber ? 'combined' :
                      registrationData.samroLicenseNumber ? 'samro_only' : 'sampra_only',
          samroLicense: registrationData.samroLicenseNumber ? {
            licenseNumber: registrationData.samroLicenseNumber,
            tariffCode: registrationData.samroTariffCode,
            isActive: true,
            expiryDate: registrationData.samroExpiryDate,
            paymentStatus: 'pending'
          } : undefined,
          sampraLicense: registrationData.sampraLicenseNumber ? {
            licenseNumber: registrationData.sampraLicenseNumber,
            tariffCode: registrationData.sampraTariffCode,
            isActive: true,
            expiryDate: registrationData.sampraExpiryDate,
            paymentStatus: 'pending'
          } : undefined,
          risaCompliance: {
            isCompliant: true,
            registrationNumber: registrationData.risaRegistrationNumber
          },
          contactPerson: {
            name: registrationData.ownerName,
            email: email.toLowerCase()
          },
          businessDetails: {
            registrationNumber: registrationData.businessRegistrationNumber,
            vatNumber: registrationData.vatNumber,
            businessType: 'company'
          },
          status: 'active'
        }], { session });
      }

      // Mark verification as used
      await verification.markAsUsed();

      // Commit transaction
      await session.commitTransaction();

      // Send welcome email
      await emailService.sendWelcomeEmail(
        email,
        registrationData.storeName,
        registrationData.ownerName,
        registrationData.ownerUsername
      );

      res.status(201).json({
        message: 'Store registration completed successfully!',
        store: {
          id: store[0]._id,
          name: store[0].name,
          email: store[0].email
        },
        user: {
          id: user[0]._id,
          username: user[0].username,
          role: user[0].role
        }
      });

    } catch (transactionError) {
      await session.abortTransaction();
      throw transactionError;
    } finally {
      session.endSession();
    }

  } catch (error) {
    console.error('Store verification error:', error);
    res.status(500).json({ 
      error: 'Verification failed. Please try again.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Resend verification code
exports.resendVerificationCode = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Find existing verification record
    const existingVerification = await EmailVerification.findOne({
      email: email.toLowerCase(),
      type: 'store_registration',
      isUsed: false
    });

    if (!existingVerification) {
      return res.status(404).json({ error: 'No pending registration found for this email' });
    }

    // Generate new verification code
    const newVerificationCode = EmailVerification.generateCode();

    // Update verification record
    existingVerification.verificationCode = newVerificationCode;
    existingVerification.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    existingVerification.attempts = 0; // Reset attempts
    await existingVerification.save();

    // Send new verification email
    const emailResult = await emailService.sendVerificationEmail(
      email,
      newVerificationCode,
      existingVerification.registrationData.storeName,
      existingVerification.registrationData.ownerName
    );

    if (!emailResult.success) {
      return res.status(500).json({ 
        error: 'Failed to send verification email. Please try again.' 
      });
    }

    res.status(200).json({
      message: 'New verification code sent successfully',
      expiresAt: existingVerification.expiresAt
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    res.status(500).json({
      error: 'Failed to resend verification code. Please try again.',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Check if license number already exists
exports.checkLicenseAvailability = async (req, res) => {
  try {
    const { licenseNumber, licenseType } = req.query;

    if (!licenseNumber || !licenseType) {
      return res.status(400).json({ error: 'License number and type are required' });
    }

    let existingLicense = null;

    if (licenseType === 'samro') {
      existingLicense = await Store.findOne({ 'licenses.samro.licenseNumber': licenseNumber });
    } else if (licenseType === 'sampra') {
      existingLicense = await Store.findOne({ 'licenses.sampra.licenseNumber': licenseNumber });
    } else if (licenseType === 'risa') {
      existingLicense = await Store.findOne({ 'licenses.risa.registrationNumber': licenseNumber });
    }

    res.json({
      available: !existingLicense,
      message: existingLicense ?
        `This ${licenseType.toUpperCase()} license number is already registered to another store` :
        `This ${licenseType.toUpperCase()} license number is available`
    });

  } catch (error) {
    console.error('License check error:', error);
    res.status(500).json({
      error: 'Failed to check license availability',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};



