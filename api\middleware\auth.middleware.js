const jwt = require('jsonwebtoken');
const { JWT_SECRET } = process.env;

const authenticate = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  if (!token) return res.status(401).json({ error: 'No token provided' });

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (err) {
    res.status(401).json({ error: 'Invalid token' });
  }
};

const authorizeAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Access denied' });
  }
  next();
};

// Authorization for compliance staff (SAMRO, SAMPRA, compliance admin)
const authorizeCompliance = (req, res, next) => {
  const complianceRoles = ['samro_staff', 'sampra_staff', 'compliance_admin', 'admin'];
  if (!complianceRoles.includes(req.user.role)) {
    return res.status(403).json({ error: 'Compliance access required' });
  }
  next();
};



// Authorization for SAMRO staff specifically
const authorizeSAMRO = (req, res, next) => {
  const allowedRoles = ['samro_staff', 'compliance_admin', 'admin'];
  if (!allowedRoles.includes(req.user.role) && req.user.organization !== 'SAMRO') {
    return res.status(403).json({ error: 'SAMRO access required' });
  }
  next();
};

// Authorization for SAMPRA staff specifically
const authorizeSAMPRA = (req, res, next) => {
  const allowedRoles = ['sampra_staff', 'compliance_admin', 'admin'];
  if (!allowedRoles.includes(req.user.role) && req.user.organization !== 'SAMPRA') {
    return res.status(403).json({ error: 'SAMPRA access required' });
  }
  next();
};

// Check specific permissions
const checkPermission = (permission) => {
  return (req, res, next) => {
    if (!req.user.permissions || !req.user.permissions.includes(permission)) {
      return res.status(403).json({ error: `Permission '${permission}' required` });
    }
    next();
  };
};

// Authorization for audit trail access - only compliance officers and admins, not SAMRO/SAMPRA staff
const authorizeAuditTrail = (req, res, next) => {
  const auditTrailRoles = ['admin', 'compliance_admin'];
  const hasAuditPermission = req.user.permissions && req.user.permissions.includes('audit_trail_access');

  if (!auditTrailRoles.includes(req.user.role) && !hasAuditPermission) {
    return res.status(403).json({ error: 'Audit trail access permission required' });
  }
  next();
};

// Authorization for data export operations
const authorizeDataExport = (req, res, next) => {
  const exportRoles = ['admin', 'compliance_admin', 'samro_staff', 'sampra_staff'];
  const hasExportPermission = req.user.permissions && req.user.permissions.includes('export_compliance_data');

  if (!exportRoles.includes(req.user.role) && !hasExportPermission) {
    return res.status(403).json({ error: 'Data export permission required' });
  }
  next();
};

// Generic role-based authorization middleware
const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user || !req.user.role) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: `Access denied. Required roles: ${allowedRoles.join(', ')}`
      });
    }

    next();
  };
};

// Check if user can edit a specific license based on organization
const checkLicenseEditPermission = async (req, res, next) => {
  try {
    const { id } = req.params;
    const VenueLicense = require('../models/VenueLicense.model');

    const license = await VenueLicense.findById(id);
    if (!license) {
      return res.status(404).json({ error: 'License not found' });
    }

    // Admins and compliance admins can edit all licenses
    if (req.user.role === 'admin' || req.user.role === 'compliance_admin') {
      return next();
    }

    // SAMRO staff can only edit SAMRO licenses
    if (req.user.role === 'samro_staff' || req.user.organization === 'SAMRO') {
      const canEditSAMRO = license.licenseType === 'SAMRO' ||
                          (license.samroLicense && license.samroLicense.isActive);
      if (!canEditSAMRO) {
        return res.status(403).json({
          error: 'SAMRO staff can only edit SAMRO licenses'
        });
      }
      return next();
    }

    // SAMPRA staff can only edit SAMPRA licenses
    if (req.user.role === 'sampra_staff' || req.user.organization === 'SAMPRA') {
      const canEditSAMPRA = license.licenseType === 'SAMPRA' ||
                           (license.sampraLicense && license.sampraLicense.isActive);
      if (!canEditSAMPRA) {
        return res.status(403).json({
          error: 'SAMPRA staff can only edit SAMPRA licenses'
        });
      }
      return next();
    }

    return res.status(403).json({
      error: 'Insufficient permissions to edit this license'
    });
  } catch (error) {
    console.error('Error checking license edit permission:', error);
    return res.status(500).json({ error: 'Failed to verify license permissions' });
  }
};

module.exports = {
  authenticate,
  auth: authenticate, // Alias for backward compatibility
  authorizeAdmin,
  authorizeCompliance,
  authorizeSAMRO,
  authorizeSAMPRA,

  authorizeAuditTrail,
  checkPermission,
  authorizeDataExport,
  requireRole,
  checkLicenseEditPermission
};