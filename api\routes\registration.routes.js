const express = require('express');
const router = express.Router();
const {
  registerStore,
  verifyStoreRegistration,
  resendVerificationCode,
  checkLicenseAvailability
} = require('../controllers/registration.controller');

// Store registration routes
router.post('/store', registerStore);
router.post('/store/verify', verifyStoreRegistration);
router.post('/store/resend-code', resendVerificationCode);
router.get('/check-license', checkLicenseAvailability);



module.exports = router;
