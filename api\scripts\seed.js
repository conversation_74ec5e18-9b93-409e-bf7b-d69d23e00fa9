const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const Store = require('../models/Store.model');
const User = require('../models/User.model');
const Track = require('../models/Track.model');
const Playlist = require('../models/Playlist.model');
const PlayHistory = require('../models/PlayHistory.model');
const Settings = require('../models/Settings.model');
const RadioStation = require('../models/RadioStation.model');
const AuditLog = require('../models/AuditLog.model');
const ActivityLog = require('../models/ActivityLog.model');
const ComplianceReport = require('../models/ComplianceReport.model');
const ComplianceAlert = require('../models/ComplianceAlert.model');
const SAMRORates = require('../models/SAMRORates.model');
const SAMPRARates = require('../models/SAMPRARates.model');
const VenueLicense = require('../models/VenueLicense.model');
const RoyaltyPayment = require('../models/RoyaltyPayment.model');
const Notification = require('../models/Notification.model');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected for seeding');
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

// Enhanced sample data for comprehensive analytics
const storesData = [
  {
    name: 'Test Store 1',
    email: '<EMAIL>',
    address: '123 Main Street, Cape Town, South Africa',
    city: 'Cape Town',
    province: 'Western Cape',
    country: 'South Africa',
    businessType: 'retail',
    timezone: 'Africa/Johannesburg',
    verificationStatus: 'verified',
    licenses: {
      samro: {
        licenseNumber: 'SAMRO-001',
        tariffCode: 'A1',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      sampra: {
        licenseNumber: 'SAMPRA-001',
        tariffCode: 'RT1',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      risa: {
        registrationNumber: 'RISA-2025-001234',
        isCompliant: true
      }
    },
    openHours: {
      monday: { open: '09:00', close: '21:00', closed: false },
      tuesday: { open: '09:00', close: '21:00', closed: false },
      wednesday: { open: '09:00', close: '21:00', closed: false },
      thursday: { open: '09:00', close: '21:00', closed: false },
      friday: { open: '09:00', close: '22:00', closed: false },
      saturday: { open: '09:00', close: '22:00', closed: false },
      sunday: { open: '10:00', close: '20:00', closed: false }
    },
    devices: ['device-001', 'device-002']
  },
  {
    name: 'Test Store 2',
    email: '<EMAIL>',
    address: '456 Shopping Center, Johannesburg, South Africa',
    city: 'Johannesburg',
    province: 'Gauteng',
    country: 'South Africa',
    businessType: 'retail',
    timezone: 'Africa/Johannesburg',
    verificationStatus: 'verified',
    licenses: {
      samro: {
        licenseNumber: 'SAMRO-002',
        tariffCode: 'B2',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      sampra: {
        licenseNumber: 'SAMPRA-002',
        tariffCode: 'RT2',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      risa: {
        registrationNumber: 'RISA-2025-005678',
        isCompliant: true
      }
    },
    openHours: {
      monday: { open: '10:00', close: '22:00', closed: false },
      tuesday: { open: '10:00', close: '22:00', closed: false },
      wednesday: { open: '10:00', close: '22:00', closed: false },
      thursday: { open: '10:00', close: '22:00', closed: false },
      friday: { open: '10:00', close: '23:00', closed: false },
      saturday: { open: '09:00', close: '23:00', closed: false },
      sunday: { open: '10:00', close: '21:00', closed: false }
    },
    devices: ['device-003', 'device-004']
  },
  {
    name: 'Test Store 3',
    email: '<EMAIL>',
    address: '789 Beach Road, Durban, South Africa',
    city: 'Durban',
    province: 'KwaZulu-Natal',
    country: 'South Africa',
    businessType: 'retail',
    timezone: 'Africa/Johannesburg',
    verificationStatus: 'verified',
    licenses: {
      samro: {
        licenseNumber: 'SAMRO-003',
        tariffCode: 'A2',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      sampra: {
        licenseNumber: 'SAMPRA-003',
        tariffCode: 'RT1',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      risa: {
        registrationNumber: 'RISA-2025-009012',
        isCompliant: true
      }
    },
    openHours: {
      monday: { open: '08:00', close: '20:00', closed: false },
      tuesday: { open: '08:00', close: '20:00', closed: false },
      wednesday: { open: '08:00', close: '20:00', closed: false },
      thursday: { open: '08:00', close: '20:00', closed: false },
      friday: { open: '08:00', close: '21:00', closed: false },
      saturday: { open: '08:00', close: '21:00', closed: false },
      sunday: { open: '09:00', close: '19:00', closed: false }
    },
    devices: ['device-005']
  },
  {
    name: 'Test Store 4',
    email: '<EMAIL>',
    address: '321 Trendy Street, Pretoria, South Africa',
    city: 'Pretoria',
    province: 'Gauteng',
    country: 'South Africa',
    businessType: 'restaurant',
    timezone: 'Africa/Johannesburg',
    verificationStatus: 'verified',
    licenses: {
      samro: {
        licenseNumber: 'SAMRO-004',
        tariffCode: 'B1',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      sampra: {
        licenseNumber: 'SAMPRA-004',
        tariffCode: 'RT1',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      risa: {
        registrationNumber: 'RISA-2025-004',
        isCompliant: true
      }
    },
    openHours: {
      monday: { open: '07:00', close: '22:00', closed: false },
      tuesday: { open: '07:00', close: '22:00', closed: false },
      wednesday: { open: '07:00', close: '22:00', closed: false },
      thursday: { open: '07:00', close: '22:00', closed: false },
      friday: { open: '07:00', close: '23:00', closed: false },
      saturday: { open: '08:00', close: '23:00', closed: false },
      sunday: { open: '08:00', close: '21:00', closed: false }
    },
    devices: ['device-006', 'device-007']
  },
  {
    name: 'Test Store 5',
    email: '<EMAIL>',
    address: '654 Health Avenue, Port Elizabeth, South Africa',
    city: 'Port Elizabeth',
    province: 'Eastern Cape',
    country: 'South Africa',
    businessType: 'gym',
    timezone: 'Africa/Johannesburg',
    verificationStatus: 'verified',
    licenses: {
      samro: {
        licenseNumber: 'SAMRO-005',
        tariffCode: 'C1',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      sampra: {
        licenseNumber: 'SAMPRA-005',
        tariffCode: 'RT2',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      risa: {
        registrationNumber: 'RISA-2025-005',
        isCompliant: true
      }
    },
    openHours: {
      monday: { open: '05:00', close: '22:00', closed: false },
      tuesday: { open: '05:00', close: '22:00', closed: false },
      wednesday: { open: '05:00', close: '22:00', closed: false },
      thursday: { open: '05:00', close: '22:00', closed: false },
      friday: { open: '05:00', close: '21:00', closed: false },
      saturday: { open: '06:00', close: '20:00', closed: false },
      sunday: { open: '07:00', close: '19:00', closed: false }
    },
    devices: ['device-008']
  },
  {
    name: 'Test Store 6',
    email: '<EMAIL>',
    address: '987 Luxury Lane, Sandton, South Africa',
    city: 'Sandton',
    province: 'Gauteng',
    country: 'South Africa',
    businessType: 'hotel',
    timezone: 'Africa/Johannesburg',
    verificationStatus: 'verified',
    licenses: {
      samro: {
        licenseNumber: 'SAMRO-006',
        tariffCode: 'C2',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      sampra: {
        licenseNumber: 'SAMPRA-006',
        tariffCode: 'RT3',
        expiryDate: new Date('2025-12-31'),
        isActive: true
      },
      risa: {
        registrationNumber: 'RISA-2024-006',
        isCompliant: true
      }
    },
    openHours: {
      monday: { open: '00:00', close: '23:59', closed: false },
      tuesday: { open: '00:00', close: '23:59', closed: false },
      wednesday: { open: '00:00', close: '23:59', closed: false },
      thursday: { open: '00:00', close: '23:59', closed: false },
      friday: { open: '00:00', close: '23:59', closed: false },
      saturday: { open: '00:00', close: '23:59', closed: false },
      sunday: { open: '00:00', close: '23:59', closed: false }
    },
    devices: ['device-009', 'device-010']
  }
];

// Compliance users data
const complianceUsersData = [
  {
    username: 'samro_admin',
    email: '<EMAIL>',
    password: 'samro123',
    role: 'samro_staff',
    organization: 'SAMRO',
    permissions: [
      'view_all_reports',
      'export_compliance_data',
      'real_time_monitoring',
      'data_verification',
      'create_licenses',
      'edit_licenses',
      'delete_licenses'
    ],
    emailVerified: true,
    accountStatus: 'active'
  },
  {
    username: 'sampra_admin',
    email: '<EMAIL>',
    password: 'sampra123',
    role: 'sampra_staff',
    organization: 'SAMPRA',
    permissions: [
      'view_all_reports',
      'export_compliance_data',
      'real_time_monitoring',
      'data_verification',
      'create_licenses',
      'edit_licenses',
      'delete_licenses'
    ],
    emailVerified: true,
    accountStatus: 'active'
  },
  {
    username: 'compliance_officer',
    email: '<EMAIL>',
    password: 'compliance123',
    role: 'compliance_admin',
    organization: 'Internal',
    permissions: [
      'view_all_reports',
      'export_compliance_data',
      'audit_trail_access',
      'real_time_monitoring',
      'data_verification',
      'cross_organization_access',
      'create_licenses',
      'edit_licenses',
      'delete_licenses'
    ],
    emailVerified: true,
    accountStatus: 'active'
  },
  {
    username: 'samro_staff1',
    email: '<EMAIL>',
    password: 'samro456',
    role: 'samro_staff',
    organization: 'SAMRO',
    permissions: [
      'view_all_reports',
      'export_compliance_data',
      'real_time_monitoring',
      'data_verification',
      'create_licenses',
      'edit_licenses',
      'delete_licenses'
    ],
    emailVerified: true,
    accountStatus: 'active'
  },
  {
    username: 'sampra_staff1',
    email: '<EMAIL>',
    password: 'sampra456',
    role: 'sampra_staff',
    organization: 'SAMPRA',
    permissions: [
      'view_all_reports',
      'export_compliance_data',
      'real_time_monitoring',
      'data_verification',
      'create_licenses',
      'edit_licenses',
      'delete_licenses'
    ],
    emailVerified: true,
    accountStatus: 'active'
  }
];

// Radio stations data
const radioStationsData = [
  {
    name: 'Safm',
    url: 'https://25453.live.streamtheworld.com/SAFM.mp3',
    frequency: '104.7',
    genre: 'All',
    country: 'ZA',
    description: 'Where the Music Matters - Alternative and indie music from Seattle',
    isActive: true
  },
  {
    name: 'Radio 2000',
    url: 'https://22663.live.streamtheworld.com/RADIO2000.mp3',
    frequency: '97.5',
    genre: 'All',
    country: 'ZA',
    description: 'Commercial-free, listener-supported radio with eclectic music',
    isActive: true
  },
  {
    name: 'Umhlobo Wenene FM',
    url: 'https://29093.live.streamtheworld.com/UMHLOBOWENENE.mp3',
    frequency: '91.9',
    genre: 'All',
    country: 'ZA',
    description: 'The best in smooth jazz music 24/7',
    isActive: true
  },
  {
    name: 'Motsweding FM',
    url: 'https://25453.live.streamtheworld.com/MOTSWEDING.mp3',
    frequency: '88.8',
    genre: 'All',
    country: 'ZA',
    description: 'Classical music',
    isActive: true
  },
  {
    name: 'Ukhozi FM',
    url: 'https://28563.live.streamtheworld.com/UKHOZIFM.mp3',
    frequency: '88.590.8',
    genre: 'All',
    country: 'ZA',
    description: '24/7 music from around the world',
    isActive: true
  },
  {
    name: '5FM SABC',
    url: 'https://25323.live.streamtheworld.com/5FM.mp3',
    frequency: '89.9',
    genre: 'All',
    country: 'ZA',
    description: 'The biggest hits and the latest new music',
    isActive: true
  },
  {
    name: 'Lesedi FM',
    url: 'https://27793.live.streamtheworld.com/LESEDI.mp3',
    frequency: '88.4',
    genre: 'All',
    country: 'ZA',
    description: 'More Music Variety',
    isActive: true
  },
  {
    name: 'Metro FM',
    url: 'https://25503.live.streamtheworld.com/METROFM.mp3',
    frequency: '88.0',
    genre: 'All',
    country: 'ZA',
    description: 'Pure music 24/7',
    isActive: true
  }
];

// Comprehensive South African tracks data for analytics
const tracksData = [
  // Amapiano tracks
  { title: 'Mntase Vuka', artist: 'Mkeyz ft MDU aka TRP, Djy Vino & Da Ish', album: 'Mntase Vuka', genre: 'Amapiano', duration: 482 },
  { title: 'Areyeng Ke Game', artist: 'Kabza De Small ft Djy Vino & Xduppy', album: 'Areyeng Ke Game', genre: 'Amapiano', duration: 507 },
  { title: 'Yiyo', artist: 'Mr Luu ft Major Habee & DaMistro', album: 'Yiyo', genre: 'Amapiano', duration: 362 },
  { title: 'BAFUNA THINA', artist: 'Ceehle & Major Keys ft TitoM & Yuppe', album: 'BAFUNA THINA', genre: 'Amapiano', duration: 420 },
  { title: 'Piki Piki', artist: 'KMAT, ShaunMusiq & Khalil Harrison ft Moonchild Sanelly', album: 'Piki Piki', genre: 'Amapiano', duration: 303 },
  { title: 'Bawo', artist: 'Ntokzin, Azana & De Mthuda', album: 'Bawo', genre: 'Amapiano', duration: 368 },
  { title: 'Umuzi Ka Baba', artist: 'Vyno Keys, TMan Xpress & Cowboii ft Tshepo Keyz', album: 'Umuzi Ka Baba', genre: 'Amapiano', duration: 487 },
  { title: 'Amaphupho', artist: 'The Bless ft Sam Deep & AcMusiQ', album: 'Amaphupho', genre: 'Amapiano', duration: 312 },
  { title: 'Samba 8', artist: 'JayJayy, 031choppa & ShakaMan YKTV ft Shakes & Les, Jussgigi, RishBeats & Malemon', album: 'Samba 8', genre: 'Amapiano', duration: 435 },
  { title: 'iMoto Ka Bliss', artist: 'Mdoovar, 9umba & Sbu YDN ft Blissful Sax', album: 'iMoto Ka Bliss', genre: 'Amapiano', duration: 480 },

  // Hip Hop tracks
  { title: 'Dlala Vilakazi', artist: 'Kwesta', album: 'Dlala Vilakazi', genre: 'Hip Hop', duration: 247 },
  { title: 'FTYD', artist: 'M.anifest & A-Reece', album: 'FTYD', genre: 'Hip Hop', duration: 188 },
  { title: 'Abazazi Bafunani', artist: 'Big Zule ft Emtee', album: 'Abazazi Bafunani', genre: 'Hip Hop', duration: 209 },
  { title: 'Same Thing', artist: 'Yanga Chief ft. Stogie T & Frank Casino', album: 'Same Thing', genre: 'Hip Hop', duration: 187 },
  { title: 'What If? (Mngani)', artist: 'Yanga Chief', album: 'What If? (Mngani)', genre: 'Hip Hop', duration: 183 },

  // Gqom tracks
  { title: 'Vutha Dubane', artist: 'We Dem Boyz ft. Dankie Boi, Eemoh & Okmalumkoolkat', album: 'Vutha Dubane', genre: 'Gqom', duration: 302 },
  { title: 'Zasha', artist: 'DJ Tira, DJ Maphorisa & Kabza De Small ft. Leehleza, Dladla Mshunqisi, Uncool MC & Captain', album: 'Zasha', genre: 'Gqom', duration: 369 },
  { title: 'Vala', artist: 'Dankie Boi, GoldMax & Blacks Jnr ft. Dladla Mshunqisi & Black Catz', album: 'Vala', genre: 'Gqom', duration: 308 },
  { title: 'Intekhulumayo', artist: 'K.C Driller, Mr Nation Thingz & Visca ft. Cuba Beats', album: 'Intekhulumayo', genre: 'Gqom', duration: 368 },
  { title: 'Ismokolo', artist: 'Campmasters ft. Zeh McGeba & Captain', album: 'Ismokolo', genre: 'Gqom', duration: 309 },

  // House tracks
  { title: 'uMA weNGANE', artist: 'Thukuthela, Jazzworx & Sykes ft. Major League DJz', album: 'uMA weNGANE', genre: 'House', duration: 381 },
  { title: 'Ulwembu', artist: 'Mbalenhle M, Maline Aura & Section Five', album: 'Ulwembu', genre: 'House', duration: 364 },
  { title: '1000 Seconds (Atmos Blaq Remix)', artist: 'Blackwhole', album: '1000 Seconds', genre: 'House', duration: 424 },
  { title: 'Alegria (Extended Mix)', artist: 'THEMBA & DJ Kent', album: 'Alegria', genre: 'House', duration: 365 },
  { title: 'Midnight (Black Motion Remix)', artist: 'Black Motion & 340ml', album: 'Midnight', genre: 'House', duration: 373 },
];





// Seed function
const seedDatabase = async () => {
  try {
    // Clear existing data
    console.log('Clearing existing data...');
    await Store.deleteMany({});
    await User.deleteMany({});
    await Track.deleteMany({});
    await Playlist.deleteMany({});
    await PlayHistory.deleteMany({});
    await Settings.deleteMany({});
    await RadioStation.deleteMany({});
    await AuditLog.deleteMany({});
    await ActivityLog.deleteMany({});
    await ComplianceReport.deleteMany({});
    await ComplianceAlert.deleteMany({});
    await SAMRORates.deleteMany({});
    await SAMPRARates.deleteMany({});
    await VenueLicense.deleteMany({});
    await RoyaltyPayment.deleteMany({});
    await Notification.deleteMany({});

    // Create stores
    console.log('Creating stores...');
    const stores = await Store.insertMany(storesData);
    console.log(`Created ${stores.length} stores`);

    // Create admin user
    console.log('Creating users...');
    const hashedPassword = await bcrypt.hash('admin123', 10);

    const adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      permissions: [
        'view_all_reports',
        'export_compliance_data',
        'audit_trail_access',
        'real_time_monitoring',
        'data_verification',
        'cross_organization_access',
        'create_licenses',
        'edit_licenses',
        'delete_licenses'
      ],
      emailVerified: true,
      accountStatus: 'active'
    });

    // Create store users
    const storeUsers = [];
    for (let i = 0; i < stores.length; i++) {
      const storePassword = await bcrypt.hash(`store${i + 1}123`, 10);
      const storeUser = await User.create({
        username: `store${i + 1}`,
        email: `store${i + 1}@example.com`,
        password: storePassword,
        role: 'store',
        storeId: stores[i]._id,
        emailVerified: true,
        accountStatus: 'active'
      });
      storeUsers.push(storeUser);

      // Update store with user reference
      stores[i].users.push(storeUser._id);
      await stores[i].save();
    }

    // Create compliance users
    console.log('Creating compliance users...');
    const complianceUsers = [];
    for (const userData of complianceUsersData) {
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      const user = await User.create({
        ...userData,
        password: hashedPassword
      });
      complianceUsers.push(user);
    }

    console.log(`Created 1 admin user, ${storeUsers.length} store users, and ${complianceUsers.length} compliance users`);

    // Create tracks with comprehensive compliance data
    console.log('Creating tracks with comprehensive compliance data...');
    const tracks = [];
    for (let i = 0; i < tracksData.length; i++) {
      const trackData = tracksData[i];
      const timestamp = Date.now() + i;
      const track = await Track.create({
        trackId: `T-${timestamp}-${i}`,
        title: trackData.title,
        artist: trackData.artist,
        album: trackData.album,
        genre: trackData.genre,
        duration: trackData.duration,
        filePath: `/uploads/sample-${i + 1}.mp3`,
        addedBy: adminUser._id,

        // Enhanced compliance data
        compliance: {
          samroRegistered: Math.random() > 0.1,
          sampraRegistered: Math.random() > 0.15,
          risaCompliant: Math.random() > 0.05,
          verificationStatus: Math.random() > 0.2 ? 'verified' : 'pending',
          lastVerified: new Date()
        },

        // Enhanced metadata with proper compliance codes
        isrcCode: `ZA-${String(Math.floor(Math.random() * 999)).padStart(3, '0')}-${new Date().getFullYear().toString().slice(-2)}-${String(i + 1).padStart(5, '0')}`,
        iswcCode: `T-${String(Math.floor(Math.random() * 999999999)).padStart(9, '0')}-${Math.floor(Math.random() * 10)}`,

        // SAMRO work numbers
        samroWorkNumber: `SAMRO-${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,

        // Composers with IPI numbers
        composers: [{
          name: trackData.artist.split(' ft.')[0], // Main artist as composer
          ipiNumber: `I-${String(Math.floor(Math.random() * 999999999)).padStart(9, '0')}-${Math.floor(Math.random() * 10)}`,
          role: 'composer',
          share: Math.floor(Math.random() * 50) + 50 // 50-100% share
        }],

        // Publishers
        publishers: [{
          name: `${trackData.artist.split(' ft.')[0]} Publishing`,
          publisherCode: `PUB-${String(Math.floor(Math.random() * 9999)).padStart(4, '0')}`,
          share: Math.floor(Math.random() * 50) + 50,
          territory: 'ZA',
          ipiNumber: `I-${String(Math.floor(Math.random() * 999999999)).padStart(9, '0')}-${Math.floor(Math.random() * 10)}`
        }],

        // Recording rights splits for SAMPRA
        recordingRightsSplits: [{
          artistName: trackData.artist.split(' ft.')[0],
          sampraNumber: `SAMPRA-${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
          role: 'performer',
          percentage: Math.floor(Math.random() * 40) + 60 // 60-100%
        }],

        // Record label info
        recordLabel: {
          name: `${trackData.artist.split(' ft.')[0]} Records`,
          catalogNumber: `CAT-${String(Math.floor(Math.random() * 99999)).padStart(5, '0')}`,
          releaseDate: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)), // Random date within last year
          territory: 'ZA'
        },

        // Additional metadata
        metadata: {
          bpm: Math.floor(Math.random() * 60) + 80, // 80-140 BPM
          key: ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'][Math.floor(Math.random() * 12)],
          language: 'en',
          explicit: Math.random() > 0.9,
          instrumental: Math.random() > 0.95
        }
      });
      tracks.push(track);
    }
    console.log(`Created ${tracks.length} tracks with comprehensive compliance data`);

    // Create playlists
    console.log('Creating playlists...');
    const playlists = [];

    // Morning playlist for store 1
    const morningPlaylist = await Playlist.create({
      name: 'Morning Vibes',
      storeId: stores[0]._id,
      schedule: [
        { day: 'Monday', startTime: '09:00', endTime: '12:00' },
        { day: 'Tuesday', startTime: '09:00', endTime: '12:00' },
        { day: 'Wednesday', startTime: '09:00', endTime: '12:00' },
        { day: 'Thursday', startTime: '09:00', endTime: '12:00' },
        { day: 'Friday', startTime: '09:00', endTime: '12:00' }
      ],
      tracks: tracks.slice(0, 8).map(t => t._id),
      createdBy: storeUsers[0]._id
    });
    playlists.push(morningPlaylist);

    // Afternoon playlist for store 1
    const afternoonPlaylist = await Playlist.create({
      name: 'Afternoon Energy',
      storeId: stores[0]._id,
      schedule: [
        { day: 'Monday', startTime: '12:00', endTime: '17:00' },
        { day: 'Tuesday', startTime: '12:00', endTime: '17:00' },
        { day: 'Wednesday', startTime: '12:00', endTime: '17:00' },
        { day: 'Thursday', startTime: '12:00', endTime: '17:00' },
        { day: 'Friday', startTime: '12:00', endTime: '17:00' }
      ],
      tracks: tracks.slice(8, 16).map(t => t._id),
      createdBy: storeUsers[0]._id
    });
    playlists.push(afternoonPlaylist);

    // Weekend playlist for store 2
    const weekendPlaylist = await Playlist.create({
      name: 'Weekend Hits',
      storeId: stores[1]._id,
      schedule: [
        { day: 'Saturday', startTime: '10:00', endTime: '22:00' },
        { day: 'Sunday', startTime: '10:00', endTime: '20:00' }
      ],
      tracks: tracks.slice(0, 12).map(t => t._id),
      createdBy: storeUsers[1]._id
    });
    playlists.push(weekendPlaylist);

    // Chill playlist for store 3
    const chillPlaylist = await Playlist.create({
      name: 'Chill & Relax',
      storeId: stores[2]._id,
      schedule: [
        { day: 'Monday', startTime: '08:00', endTime: '20:00' },
        { day: 'Tuesday', startTime: '08:00', endTime: '20:00' },
        { day: 'Wednesday', startTime: '08:00', endTime: '20:00' },
        { day: 'Thursday', startTime: '08:00', endTime: '20:00' },
        { day: 'Friday', startTime: '08:00', endTime: '20:00' },
        { day: 'Saturday', startTime: '08:00', endTime: '20:00' },
        { day: 'Sunday', startTime: '08:00', endTime: '20:00' }
      ],
      tracks: tracks.slice(5, 15).map(t => t._id),
      createdBy: storeUsers[2]._id
    });
    playlists.push(chillPlaylist);

    console.log(`Created ${playlists.length} playlists`);

    // Create radio stations
    console.log('Creating radio stations...');
    const radioStations = [];
    for (const stationData of radioStationsData) {
      const station = await RadioStation.create({
        ...stationData,
        createdBy: adminUser._id
      });
      radioStations.push(station);
    }
    console.log(`Created ${radioStations.length} radio stations`);

    // Create SAMRO rates
    console.log('Creating SAMRO rates...');
    const samroRatesData = [
      {
        rateId: 'SAMRO-A1-2024',
        category: 'retail',
        subcategory: 'small_retail',
        rateStructure: 'flat_rate',
        rates: {
          baseRate: 150.00,
          currency: 'ZAR',
          unit: 'per_month',
          minimumFee: 100.00,
          maximumFee: 200.00,
          administrationFee: 15.00,
          vatRate: 15
        },
        venueBracket: 'small',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2025-12-31'),
        isActive: true,
        description: 'Small retail stores - Tariff A1',
        legalReference: 'SAMRO Tariff A1 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMRO-A2-2024',
        category: 'retail',
        subcategory: 'medium_retail',
        rateStructure: 'flat_rate',
        rates: {
          baseRate: 300.00,
          currency: 'ZAR',
          unit: 'per_month',
          minimumFee: 200.00,
          maximumFee: 400.00,
          administrationFee: 30.00,
          vatRate: 15
        },
        venueBracket: 'medium',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2025-12-31'),
        isActive: true,
        description: 'Medium retail stores - Tariff A2',
        legalReference: 'SAMRO Tariff A2 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMRO-B1-2024',
        category: 'restaurant',
        subcategory: 'small_restaurant',
        rateStructure: 'flat_rate',
        rates: {
          baseRate: 250.00,
          currency: 'ZAR',
          unit: 'per_month',
          minimumFee: 150.00,
          maximumFee: 350.00,
          administrationFee: 25.00,
          vatRate: 15
        },
        venueBracket: 'small',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2025-12-31'),
        isActive: true,
        description: 'Restaurants/Cafes - Tariff B1',
        legalReference: 'SAMRO Tariff B1 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMRO-B2-2024',
        category: 'restaurant',
        subcategory: 'large_restaurant',
        rateStructure: 'flat_rate',
        rates: {
          baseRate: 500.00,
          currency: 'ZAR',
          unit: 'per_month',
          minimumFee: 300.00,
          maximumFee: 700.00,
          administrationFee: 50.00,
          vatRate: 15
        },
        venueBracket: 'large',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        isActive: true,
        description: 'Large restaurants - Tariff B2',
        legalReference: 'SAMRO Tariff B2 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMRO-C1-2024',
        category: 'hotel',
        subcategory: 'small_hotel',
        rateStructure: 'flat_rate',
        rates: {
          baseRate: 400.00,
          currency: 'ZAR',
          unit: 'per_month',
          minimumFee: 250.00,
          maximumFee: 550.00,
          administrationFee: 40.00,
          vatRate: 15
        },
        venueBracket: 'small',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        isActive: true,
        description: 'Hotels/Lodges - Tariff C1',
        legalReference: 'SAMRO Tariff C1 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMRO-C2-2024',
        category: 'hotel',
        subcategory: 'large_hotel',
        rateStructure: 'flat_rate',
        rates: {
          baseRate: 800.00,
          currency: 'ZAR',
          unit: 'per_month',
          minimumFee: 500.00,
          maximumFee: 1100.00,
          administrationFee: 80.00,
          vatRate: 15
        },
        venueBracket: 'large',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        isActive: true,
        description: 'Large hotels - Tariff C2',
        legalReference: 'SAMRO Tariff C2 2024',
        createdBy: adminUser._id
      }
    ];

    const samroRates = await SAMRORates.insertMany(samroRatesData);
    console.log(`Created ${samroRates.length} SAMRO rate structures`);

    // Create SAMPRA rates
    console.log('Creating SAMPRA rates...');
    const sampraRatesData = [
      {
        rateId: 'SAMPRA-RT1-2024',
        category: 'retail',
        subcategory: 'small_venue',
        rateStructure: 'flat_rate',
        rates: {
          artistRate: 50.00,
          producerRate: 25.00,
          labelRate: 25.00,
          currency: 'ZAR',
          unit: 'per_month',
          splits: {
            artistPercentage: 50,
            producerPercentage: 25,
            labelPercentage: 25
          },
          minimumFee: 60.00,
          maximumFee: 150.00,
          administrationFee: 10.00,
          vatRate: 15
        },
        venueBracket: 'small',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        isActive: true,
        description: 'Small venues - Tariff RT1',
        legalReference: 'SAMPRA Tariff RT1 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMPRA-RT2-2024',
        category: 'retail',
        subcategory: 'medium_venue',
        rateStructure: 'flat_rate',
        rates: {
          artistRate: 100.00,
          producerRate: 50.00,
          labelRate: 50.00,
          currency: 'ZAR',
          unit: 'per_month',
          splits: {
            artistPercentage: 50,
            producerPercentage: 25,
            labelPercentage: 25
          },
          minimumFee: 120.00,
          maximumFee: 300.00,
          administrationFee: 20.00,
          vatRate: 15
        },
        venueBracket: 'medium',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        isActive: true,
        description: 'Medium venues - Tariff RT2',
        legalReference: 'SAMPRA Tariff RT2 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMPRA-RT3-2024',
        category: 'public_venue',
        subcategory: 'large_venue',
        rateStructure: 'flat_rate',
        rates: {
          artistRate: 200.00,
          producerRate: 100.00,
          labelRate: 100.00,
          currency: 'ZAR',
          unit: 'per_month',
          splits: {
            artistPercentage: 50,
            producerPercentage: 25,
            labelPercentage: 25
          },
          minimumFee: 240.00,
          maximumFee: 600.00,
          administrationFee: 40.00,
          vatRate: 15
        },
        venueBracket: 'large',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        isActive: true,
        description: 'Large venues - Tariff RT3',
        legalReference: 'SAMPRA Tariff RT3 2024',
        createdBy: adminUser._id
      },
      {
        rateId: 'SAMPRA-RT4-2024',
        category: 'public_venue',
        subcategory: 'commercial_venue',
        rateStructure: 'flat_rate',
        rates: {
          artistRate: 300.00,
          producerRate: 150.00,
          labelRate: 150.00,
          currency: 'ZAR',
          unit: 'per_month',
          splits: {
            artistPercentage: 50,
            producerPercentage: 25,
            labelPercentage: 25
          },
          minimumFee: 360.00,
          maximumFee: 900.00,
          administrationFee: 60.00,
          vatRate: 15
        },
        venueBracket: 'enterprise',
        territory: 'ZA',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        isActive: true,
        description: 'Commercial venues - Tariff RT4',
        legalReference: 'SAMPRA Tariff RT4 2024',
        createdBy: adminUser._id
      }
    ];

    const sampraRates = await SAMPRARates.insertMany(sampraRatesData);
    console.log(`Created ${sampraRates.length} SAMPRA rate structures`);

    // Create venue licenses for stores
    console.log('Creating venue licenses...');
    const venueLicenses = [];
    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];

      // Debug: Check if store has licenses
      if (!store.licenses) {
        console.log(`Warning: Store ${store.name} has no licenses object`);
        continue;
      }

      // Generate unique license ID
      const licenseId = `VL-${Date.now()}-${store._id.toString().slice(-6)}-${i}`;

      const license = await VenueLicense.create({
        licenseId: licenseId,
        venue: {
          storeId: store._id,
          name: store.name,
          address: {
            street: store.address,
            city: store.city,
            province: store.province,
            postalCode: store.postalCode || '',
            country: store.country || 'South Africa'
          },
          venueType: store.businessType || 'retail'
        },
        licenseType: 'combined',
        samroLicense: {
          licenseNumber: store.licenses?.samro?.licenseNumber || `SAMRO-${Date.now()}-${i}`,
          tariffCode: store.licenses?.samro?.tariffCode || 'A1',
          category: store.licenses?.samro?.tariffCode || 'A1',
          isActive: true,
          issueDate: new Date(),
          expiryDate: store.licenses?.samro?.expiryDate || new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
          annualFee: 5000,
          paymentStatus: 'paid',
          lastPaymentDate: new Date(),
          nextPaymentDue: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
        },
        sampraLicense: {
          licenseNumber: store.licenses?.sampra?.licenseNumber || `SAMPRA-${Date.now()}-${i}`,
          tariffCode: store.licenses?.sampra?.tariffCode || 'RT1',
          category: store.licenses?.sampra?.tariffCode || 'RT1',
          isActive: true,
          issueDate: new Date(),
          expiryDate: store.licenses?.sampra?.expiryDate || new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
          annualFee: 3000,
          paymentStatus: 'paid',
          lastPaymentDate: new Date(),
          nextPaymentDue: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
        },
        risaCompliance: {
          isCompliant: true,
          registrationNumber: store.licenses?.risa?.registrationNumber || `RISA-${Date.now()}-${i}`,
          lastAuditDate: new Date(),
          nextAuditDue: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
          complianceScore: 95
        },
        contactPerson: {
          name: `${store.name} Manager`,
          email: store.email,
          phone: '+***********',
          position: 'Store Manager'
        },
        businessDetails: {
          registrationNumber: store.businessRegistrationNumber || `REG-${Date.now()}`,
          vatNumber: store.vatNumber || `VAT-${Date.now()}`,
          businessType: 'company',
          annualRevenue: Math.floor(Math.random() * 1000000) + 500000,
          employeeCount: Math.floor(Math.random() * 50) + 10
        },
        status: 'active',
        createdBy: adminUser._id,
        updatedBy: adminUser._id,
        auditTrail: [{
          action: 'License Created',
          performedBy: adminUser._id,
          details: `Venue license created for ${store.name} during seeding`,
          timestamp: new Date()
        }]
      });
      venueLicenses.push(license);
    }
    console.log(`Created ${venueLicenses.length} venue licenses`);

    // Create comprehensive play history for analytics
    console.log('Creating comprehensive play history for analytics...');
    const playHistoryData = [];
    const now = new Date();

    // Create data for the last 60 days for better analytics
    for (let day = 0; day < 60; day++) {
      const playDate = new Date(now);
      playDate.setDate(playDate.getDate() - day);

      // Different play patterns for different days
      const isWeekend = playDate.getDay() === 0 || playDate.getDay() === 6;
      const isHoliday = Math.random() > 0.95; // Random holidays

      for (let storeIndex = 0; storeIndex < stores.length; storeIndex++) {
        const store = stores[storeIndex];

        // Vary play counts based on store type and day
        let basePlayCount = 30;
        if (store.businessType === 'gym') basePlayCount = 80;
        if (store.businessType === 'hotel') basePlayCount = 120;
        if (store.businessType === 'restaurant') basePlayCount = 60;
        if (isWeekend) basePlayCount *= 1.5;
        if (isHoliday) basePlayCount *= 0.7;

        const numPlays = Math.floor(Math.random() * basePlayCount) + Math.floor(basePlayCount * 0.5);

        // Create hourly distribution that makes sense
        const hourlyDistribution = [];
        for (let hour = 0; hour < 24; hour++) {
          let weight = 1;

          // Business hours get more plays
          if (hour >= 8 && hour <= 22) weight = 3;
          if (hour >= 10 && hour <= 20) weight = 5;
          if (hour >= 12 && hour <= 18) weight = 7; // Peak hours

          // Adjust for business type
          if (store.businessType === 'gym') {
            if (hour >= 6 && hour <= 9) weight = 8; // Morning rush
            if (hour >= 17 && hour <= 21) weight = 9; // Evening rush
          }
          if (store.businessType === 'restaurant') {
            if (hour >= 11 && hour <= 14) weight = 8; // Lunch
            if (hour >= 18 && hour <= 22) weight = 9; // Dinner
          }

          hourlyDistribution.push({ hour, weight });
        }

        for (let play = 0; play < numPlays; play++) {
          // Select track with genre preferences based on store type
          let trackPool = tracks;
          if (store.businessType === 'gym') {
            trackPool = tracks.filter(t => ['House', 'Hip Hop', 'Amapiano'].includes(t.genre));
          } else if (store.businessType === 'restaurant') {
            trackPool = tracks.filter(t => ['Jazz', 'House', 'Afrobeats'].includes(t.genre));
          } else if (store.businessType === 'hotel') {
            trackPool = tracks.filter(t => ['Jazz', 'House', 'Gospel'].includes(t.genre));
          }

          if (trackPool.length === 0) trackPool = tracks;
          const randomTrack = trackPool[Math.floor(Math.random() * trackPool.length)];

          // Select hour based on weighted distribution
          const totalWeight = hourlyDistribution.reduce((sum, h) => sum + h.weight, 0);
          let randomWeight = Math.random() * totalWeight;
          let selectedHour = 12; // Default

          for (const hourData of hourlyDistribution) {
            randomWeight -= hourData.weight;
            if (randomWeight <= 0) {
              selectedHour = hourData.hour;
              break;
            }
          }

          const startTime = new Date(playDate);
          startTime.setHours(selectedHour);
          startTime.setMinutes(Math.floor(Math.random() * 60));
          startTime.setSeconds(Math.floor(Math.random() * 60));

          // Simulate realistic play completion
          const completionRate = Math.random();
          let playbackStatus = 'completed';
          let durationPlayed = randomTrack.duration;
          let completionPercentage = 100;

          if (completionRate < 0.05) { // 5% skipped early
            playbackStatus = 'skipped';
            durationPlayed = Math.floor(randomTrack.duration * (Math.random() * 0.3 + 0.1));
            completionPercentage = Math.floor((durationPlayed / randomTrack.duration) * 100);
          } else if (completionRate < 0.15) { // 10% partial plays
            playbackStatus = 'partial';
            durationPlayed = Math.floor(randomTrack.duration * (Math.random() * 0.4 + 0.6));
            completionPercentage = Math.floor((durationPlayed / randomTrack.duration) * 100);
          }

          const endTime = new Date(startTime);
          endTime.setSeconds(endTime.getSeconds() + durationPlayed);

          const location = {
            storeAddress: store.address,
            city: store.city,
            province: store.province,
            country: store.country || 'South Africa',
            coordinates: {
              latitude: -26.2041 + (Math.random() - 0.5) * 10, // Rough SA coordinates
              longitude: 28.0473 + (Math.random() - 0.5) * 10
            }
          };

          playHistoryData.push({
            storeId: store._id,
            deviceId: store.devices[Math.floor(Math.random() * store.devices.length)],
            trackId: randomTrack._id,
            startTime: startTime,
            endTime: endTime,
            playedDate: playDate,
            durationPlayed: durationPlayed,
            totalTrackDuration: randomTrack.duration,
            playbackStatus: playbackStatus,
            completionPercentage: completionPercentage,
            location: location,

            // Enhanced metadata for analytics
            metadata: {
              timezone: 'Africa/Johannesburg',
              scheduledTime: `${selectedHour}:00`,
              scheduledDay: playDate.toLocaleDateString('en-US', { weekday: 'long' }),
              deviceType: 'music_player',
              volume: Math.floor(Math.random() * 30) + 70, // 70-100% volume
              audioQuality: ['128kbps', '192kbps', '256kbps', '320kbps'][Math.floor(Math.random() * 4)],
              sourceType: 'music_player'
            },

            // Compliance tracking
            compliance: {
              reportedToSAMRO: Math.random() > 0.05, // 95% reported
              reportedToSAMPRA: Math.random() > 0.08, // 92% reported
              reportedToRISA: Math.random() > 0.02, // 98% reported
              reportingDate: playDate,
              samroReportId: Math.random() > 0.05 ? `SAMRO-${Date.now()}-${Math.floor(Math.random() * 9999)}` : null,
              sampraReportId: Math.random() > 0.08 ? `SAMPRA-${Date.now()}-${Math.floor(Math.random() * 9999)}` : null
            }
          });
        }
      }
    }

    // Insert play history in batches to avoid memory issues
    console.log(`Inserting ${playHistoryData.length} play history records in batches...`);
    const batchSize = 1000;
    for (let i = 0; i < playHistoryData.length; i += batchSize) {
      const batch = playHistoryData.slice(i, i + batchSize);
      await PlayHistory.insertMany(batch);
      console.log(`Inserted batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(playHistoryData.length / batchSize)}`);
    }

    // Create sample compliance reports
    console.log('Creating compliance reports...');
    const complianceReports = [];
    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];

      // Generate sample track data for the report
      const sampleTracks = tracks.slice(0, Math.min(5, tracks.length));
      const trackData = sampleTracks.map(track => ({
        trackId: track._id,
        title: track.title,
        artist: track.artist,
        album: track.album,
        genre: track.genre,
        duration: track.duration,
        isrcCode: track.isrcCode,
        iswcCode: track.iswcCode,
        composers: track.composers,
        publishers: track.publishers,
        playCount: Math.floor(Math.random() * 50) + 10,
        totalDuration: (Math.floor(Math.random() * 50) + 10) * track.duration,
        compliance: {
          samroRegistered: track.compliance?.samroRegistered || false,
          sampraRegistered: track.compliance?.sampraRegistered || false,
          risaCompliant: track.compliance?.risaCompliant || true,
          verificationStatus: track.compliance?.verificationStatus || 'pending'
        }
      }));

      const totalPlays = trackData.reduce((sum, track) => sum + track.playCount, 0);
      const totalDuration = trackData.reduce((sum, track) => sum + track.totalDuration, 0);
      const uniqueArtists = new Set(trackData.map(track => track.artist)).size;

      // Generate unique report ID
      const organization = i % 2 === 0 ? 'SAMRO' : 'SAMPRA';
      const date = new Date();
      const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
      const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
      const reportId = `${organization}-${dateStr}-${randomStr}`;

      const report = new ComplianceReport({
        reportId: reportId,
        reportType: 'monthly',
        organization: organization,
        dateRange: {
          startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
          endDate: new Date(new Date().getFullYear(), new Date().getMonth(), 0)
        },
        stores: [{
          storeId: store._id,
          storeName: store.name,
          location: store.location
        }],
        trackData: trackData,
        summary: {
          totalTracks: trackData.length,
          totalPlays: totalPlays,
          totalDuration: totalDuration,
          uniqueArtists: uniqueArtists
        },
        compliance: {
          risaCompliant: true,
          samroCompliant: true,
          sampraCompliant: true,
          issues: [],
          verificationStatus: 'pending'
        },
        status: 'approved',
        generatedBy: complianceUsers[0]._id
      });
      await report.save();
      complianceReports.push(report);
    }
    console.log(`Created ${complianceReports.length} compliance reports`);

    // Create sample notifications
    console.log('Creating notifications...');
    const notifications = [];

    // Welcome notifications for all users
    const allUsers = [adminUser, ...storeUsers, ...complianceUsers];
    for (const user of allUsers) {
      const notification = await Notification.create({
        userId: user._id,
        title: 'Welcome to TrakSong',
        message: `Welcome to TrakSong Music Management System! Your account has been set up successfully.`,
        type: 'info',
        isRead: false,
        createdBy: adminUser._id
      });
      notifications.push(notification);
    }

    // License expiry notifications for stores
    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];
      const storeUser = storeUsers[i];
      const notification = await Notification.create({
        userId: storeUser._id,
        title: 'License Renewal Reminder',
        message: `Your SAMRO and SAMPRA licenses are set to expire on December 31, 2025. Please renew them to continue using the service.`,
        type: 'warning',
        isRead: false,
        createdBy: adminUser._id
      });
      notifications.push(notification);
    }

    console.log(`Created ${notifications.length} notifications`);

    // Initialize system settings
    console.log('Initializing system settings...');
    const defaultSettings = await Settings.create({
      systemName: 'TrakSong Music System',
      timezone: 'Africa/Johannesburg',
      language: 'en',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      defaultBitrate: 320,
      audioFormat: 'mp3',
      volumeLimit: 85,
      crossfadeEnabled: true,
      crossfadeDuration: 3,
      sessionTimeout: 30,
      passwordMinLength: 8,
      requireTwoFactor: false,
      allowRemoteAccess: true,
      encryptionEnabled: true,
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      systemAlerts: true,
      maintenanceAlerts: true,
      maxFileSize: 50,
      autoCleanup: true,
      cleanupDays: 30,
      compressionEnabled: true,
      autoBackup: true,
      backupFrequency: 'daily',
      backupRetention: 7,
      cloudBackup: false,
      lastModifiedBy: adminUser._id
    });
    console.log('System settings initialized');

    // Generate analytics summary
    const totalPlays = playHistoryData.length;
    const uniqueTracks = new Set(playHistoryData.map(p => p.trackId.toString())).size;
    const uniqueStores = new Set(playHistoryData.map(p => p.storeId.toString())).size;
    const genreStats = {};

    // Calculate genre statistics from play history
    for (const play of playHistoryData) {
      const track = tracks.find(t => t._id.toString() === play.trackId.toString());
      if (track) {
        genreStats[track.genre] = (genreStats[track.genre] || 0) + 1;
      }
    }

    console.log('🎵 Enhanced TrakSong Database Seeding Completed Successfully! 🎵');
    console.log('\n=== LOGIN CREDENTIALS ===');
    console.log('Admin User:');
    console.log('  Username: admin');
    console.log('  Password: admin123');
    console.log('\nStore Users:');
    for (let i = 0; i < stores.length; i++) {
      console.log(`  Store: ${stores[i].name} (${stores[i].businessType})`);
      console.log(`  Username: store${i + 1}`);
      console.log(`  Password: store${i + 1}123`);
    }
    console.log('\nCompliance Users:');
    console.log('  SAMRO Admin:');
    console.log('    Username: samro_admin');
    console.log('    Password: samro123');
    console.log('  SAMPRA Admin:');
    console.log('    Username: sampra_admin');
    console.log('    Password: sampra123');
    console.log('  Compliance Officer:');
    console.log('    Username: compliance_officer');
    console.log('    Password: compliance123');
    console.log('  SAMRO Staff:');
    console.log('    Username: samro_staff1');
    console.log('    Password: samro456');
    console.log('  SAMPRA Staff:');
    console.log('    Username: sampra_staff1');
    console.log('    Password: sampra456');

    console.log('\n=== COMPREHENSIVE DATA SUMMARY ===');
    console.log(`✅ Created ${stores.length} diverse stores (retail, restaurant, gym, hotel)`);
    console.log(`✅ Created ${1 + storeUsers.length + complianceUsers.length} users total`);
    console.log(`✅ Created ${tracks.length} South African tracks with full compliance data`);
    console.log(`✅ Created ${playlists.length} playlists`);
    console.log(`✅ Created ${radioStations.length} radio stations`);
    console.log(`✅ Created ${venueLicenses.length} venue licenses`);
    console.log(`✅ Created ${complianceReports.length} compliance reports`);
    console.log(`✅ Created ${notifications.length} notifications`);
    console.log(`✅ Created ${totalPlays.toLocaleString()} play history records (60 days)`);
    console.log(`✅ Created ${samroRates.length} SAMRO rate structures`);
    console.log(`✅ Created ${sampraRates.length} SAMPRA rate structures`);
    console.log('✅ Initialized system settings');

    console.log('\n📊 ANALYTICS DATA SUMMARY:');
    console.log(`   • Total Plays: ${totalPlays.toLocaleString()}`);
    console.log(`   • Unique Tracks: ${uniqueTracks}`);
    console.log(`   • Active Stores: ${uniqueStores}`);
    console.log(`   • Date Range: ${new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000).toDateString()} to ${now.toDateString()}`);
    console.log('   • Genre Distribution:');

    Object.entries(genreStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([genre, count]) => {
        const percentage = ((count / totalPlays) * 100).toFixed(1);
        console.log(`     - ${genre}: ${count.toLocaleString()} plays (${percentage}%)`);
      });

    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Start the backend server: npm run dev');
    console.log('2. Start the frontend: cd ../ui && npm run dev');
    console.log('3. Visit analytics dashboards:');
    console.log('   • http://localhost:5173/admin?tab=advanced-analytics');
    console.log('   • http://localhost:5173/admin?tab=analysis');
    console.log('4. No more mock data - everything is real analytics data!');
    console.log('\n🎵 Your TrakSong system is now fully seeded with comprehensive data! 🎵');
    console.log('========================\n');

  } catch (error) {
    console.error('Seeding error:', error);
  } finally {
    mongoose.connection.close();
  }
};

// Run seeding
const runSeed = async () => {
  await connectDB();
  await seedDatabase();
};

runSeed();
