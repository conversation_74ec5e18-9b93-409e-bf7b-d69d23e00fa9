const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  initializeTransporter() {
    // Configure based on EMAIL_SERVICE environment variable
    const emailService = process.env.EMAIL_SERVICE || 'gmail';

    if (emailService === 'brevo') {
      // Brevo SMTP configuration
      this.transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST || 'smtp-relay.brevo.com',
        port: parseInt(process.env.EMAIL_PORT) || 587,
        secure: process.env.EMAIL_SECURE === 'true', // true for 465, false for other ports
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD
        },
        tls: {
          rejectUnauthorized: false // Accept self-signed certificates
        }
      });
    } else if (emailService === 'ethereal' || process.env.NODE_ENV === 'development') {
      // Development configuration - use Ethereal for testing
      this.transporter = nodemailer.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        auth: {
          user: process.env.EMAIL_USER || '<EMAIL>',
          pass: process.env.EMAIL_PASSWORD || 'ethereal.pass'
        }
      });
    } else {
      // Default configuration for other services (Gmail, etc.)
      this.transporter = nodemailer.createTransporter({
        service: emailService,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD
        }
      });
    }
  }

  async sendVerificationEmail(email, verificationCode, entityName, entityType = 'store', ownerName = null) {
    const subjects = {
      store: 'Verify Your TrakSong Store Registration',
      artist: 'Verify Your TrakSong Artist Registration',
      label: 'Verify Your TrakSong Label Registration'
    };

    const mailOptions = {
      from: process.env.EMAIL_FROM || 'TrakSong System <<EMAIL>>',
      to: email,
      subject: subjects[entityType] || subjects.store,
      html: this.getVerificationEmailTemplate(verificationCode, entityName, entityType, ownerName)
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Verification email sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending verification email:', error);
      return { success: false, error: error.message };
    }
  }

  async sendWelcomeEmail(email, storeName, ownerName, username) {
    const mailOptions = {
      from: process.env.EMAIL_FROM || 'TrakSong System <<EMAIL>>',
      to: email,
      subject: 'Welcome to TrakSong - Your Store is Now Active',
      html: this.getWelcomeEmailTemplate(storeName, ownerName, username)
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Welcome email sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return { success: false, error: error.message };
    }
  }

  getVerificationEmailTemplate(verificationCode, entityName, entityType = 'store', ownerName = null) {
    const entityTypes = {
      store: { title: 'Store', greeting: 'Store Owner', features: [
        'Access your store dashboard',
        'Manage your music playlists',
        'View compliance reports',
        'Track music usage for SAMRO/SAMPRA reporting',
        'Manage your music licenses',
        'Generate royalty reports'
      ]},
      artist: { title: 'Artist', greeting: 'Artist', features: [
        'Upload and manage your tracks',
        'View detailed analytics and performance data',
        'Track plays across different venues',
        'Manage your artist profile',
        'Monitor royalty earnings',
        'Collaborate with labels and other artists'
      ]},
      label: { title: 'Label', greeting: 'Label Representative', features: [
        'Manage your artist roster',
        'Upload and distribute tracks',
        'View comprehensive analytics',
        'Manage licensing and distribution',
        'Track royalty distributions',
        'Oversee artist collaborations'
      ]}
    };

    const entity = entityTypes[entityType] || entityTypes.store;
    const displayName = ownerName || entity.greeting;
    const titleText = entityType === 'store' ? 'TrakSong Store Registration' : `TrakSong ${entity.title} Registration`;
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your ${titleText}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1976d2; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .verification-code { 
            font-size: 32px; 
            font-weight: bold; 
            color: #1976d2; 
            text-align: center; 
            padding: 20px; 
            background: white; 
            border: 2px dashed #1976d2; 
            margin: 20px 0; 
          }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${titleText}</h1>
          </div>
          <div class="content">
            <h2>Hello ${displayName},</h2>
            <p>Thank you for registering your ${entityType} "${entityName}" with TrakSong!</p>
            <p>To complete your registration and activate your account, please use the verification code below:</p>
            
            <div class="verification-code">
              ${verificationCode}
            </div>
            
            <p><strong>Important:</strong></p>
            <ul>
              <li>This code will expire in 24 hours</li>
              <li>You have 5 attempts to enter the correct code</li>
              <li>Keep this code secure and do not share it with anyone</li>
            </ul>
            
            <p>Once verified, you'll be able to:</p>
            <ul>
              ${entity.features.map(feature => `<li>${feature}</li>`).join('\n              ')}
            </ul>

            <p><strong>Note:</strong> Your account will be reviewed by our admin team for approval after verification.</p>
            
            <p>If you didn't request this registration, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>© 2024 TrakSong Music System. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  getWelcomeEmailTemplate(storeName, ownerName, username) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to TrakSong</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4caf50; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .login-info { background: white; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to TrakSong!</h1>
          </div>
          <div class="content">
            <h2>Congratulations ${ownerName}!</h2>
            <p>Your store "${storeName}" has been successfully verified and is now active in the TrakSong system.</p>
            
            <div class="login-info">
              <h3>Your Login Details:</h3>
              <p><strong>Username:</strong> ${username}</p>
              <p><strong>Store:</strong> ${storeName}</p>
              <p>You can now log in to your dashboard at: <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/login">TrakSong Login</a></p>
            </div>
            
            <h3>What's Next?</h3>
            <ul>
              <li>Set up your music playlists</li>
              <li>Configure your store devices</li>
              <li>Review and update your license information</li>
              <li>Review compliance settings</li>
              <li>Explore reporting features</li>
            </ul>
            
            <h3>Compliance & Reporting</h3>
            <p>TrakSong automatically tracks all music usage in your store for SAMRO and SAMPRA compliance reporting. You'll have access to:</p>
            <ul>
              <li>Real-time play tracking</li>
              <li>Automated compliance reports</li>
              <li>Royalty calculation assistance</li>
              <li>Export capabilities for regulatory submissions</li>
            </ul>
            
            <p>If you need any assistance, please don't hesitate to contact our support team.</p>
          </div>
          <div class="footer">
            <p>© 2024 TrakSong Music System. All rights reserved.</p>
            <p>Need help? Contact <NAME_EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = new EmailService();
