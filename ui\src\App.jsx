import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './context/AuthContext';
import { MediaProvider } from './context/MediaContext';
import ProtectedRoute from './components/ProtectedRoute';
import OfflineIndicator from './components/OfflineIndicator';
import PersistentMediaPlayer from './components/PersistentMediaPlayer';
import Login from './pages/Login';
import StoreRegistration from './pages/StoreRegistration';

import StoreDashboard from './pages/StoreDashboard';
import AdminDashboard from './pages/AdminDashboard';
import ComplianceDashboard from './pages/ComplianceDashboard';
import DataQualityDashboard from './components/DataQualityDashboard';

import materialTheme from './theme/materialTheme';
import './App.css';

function App() {
  return (
    <ThemeProvider theme={materialTheme}>
      <CssBaseline />
      <AuthProvider>
        <MediaProvider>
          <div className="min-h-screen">
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<StoreRegistration />} />

              <Route
                path="/store"
                element={
                  <ProtectedRoute requiredRole="store">
                    <StoreDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <AdminDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/compliance"
                element={
                  <ProtectedRoute requiredRole={['samro_staff', 'sampra_staff', 'compliance_admin']}>
                    <ComplianceDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/data-quality"
                element={
                  <ProtectedRoute requiredRole={['admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin']}>
                    <DataQualityDashboard />
                  </ProtectedRoute>
                }
              />

              <Route path="/" element={<Navigate to="/login" replace />} />
            </Routes>

            {/* Global Offline Indicator */}
            <OfflineIndicator />

            {/* Persistent Media Player */}
            <PersistentMediaPlayer />
          </div>
        </MediaProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
