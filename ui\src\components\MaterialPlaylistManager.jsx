import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip,
  Card,
  CardContent,
  CardActions,
  Divider,
  InputAdornment,
  Tooltip,
  CircularProgress,
  Fab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  Stack,
  useTheme,
  useMediaQuery,
  Collapse,
  SwipeableDrawer
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  VolumeUp as VolumeIcon,
  Search as SearchIcon,
  MusicNote as MusicNoteIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  PlaylistAdd as PlaylistAddIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import { playlistService, trackService } from '../services/api';
import { audioService } from '../services/audioService';
import { useAuth } from '../context/AuthContext';
import { useMedia } from '../context/MediaContext';

const MaterialPlaylistManager = () => {
  const { user } = useAuth();
  const { mediaState, togglePreview, playMusic } = useMedia();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [playlists, setPlaylists] = useState([]);
  const [tracks, setTracks] = useState([]);
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const [playlistFilter, setPlaylistFilter] = useState('all');
  const [trackFilter, setTrackFilter] = useState('all');
  const [playlistPage, setPlaylistPage] = useState(1);
  const [trackPage, setTrackPage] = useState(1);
  const [playlistsPerPage] = useState(5);
  const [tracksPerPage] = useState(12);

  // Mobile-specific state
  const [mobilePlaylistsExpanded, setMobilePlaylistsExpanded] = useState(true);
  const [mobileTracksExpanded, setMobileTracksExpanded] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [currentlyPlayingTrack, setCurrentlyPlayingTrack] = useState(null);
  const [isMainPlayerPlaying, setIsMainPlayerPlaying] = useState(false);
  const [currentPlaylistInfo, setCurrentPlaylistInfo] = useState(null);

  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      // Get store ID from user context
      const storeId = user?.storeId;
      if (!storeId) {
        console.error('No store ID found for user');
        setPlaylists([]);
        setTracks([]);
        return;
      }

      const [playlistsRes, tracksRes] = await Promise.all([
        playlistService.getByStore(storeId),
        trackService.getAll()
      ]);

      // Handle API response structure properly
      // API returns { success: true, data: [...] } or just [...]
      const playlistData = playlistsRes?.data?.data || playlistsRes?.data || [];
      const trackData = tracksRes?.data?.data || tracksRes?.data || [];

      console.log('Loaded playlists:', playlistData);
      console.log('Loaded tracks:', trackData);

      setPlaylists(Array.isArray(playlistData) ? playlistData : []);
      setTracks(Array.isArray(trackData) ? trackData : []);
    } catch (error) {
      console.error('Failed to load data:', error);
      // Ensure state is reset to empty arrays on error
      setPlaylists([]);
      setTracks([]);
    } finally {
      setLoading(false);
    }
  }, [user?.storeId]);

  useEffect(() => {
    loadData();

    // Subscribe to audio service to track currently playing track
    const unsubscribe = audioService.subscribe((state) => {
      setCurrentlyPlayingTrack(state.currentTrack);
      setIsMainPlayerPlaying(state.isPlaying);
      setCurrentPlaylistInfo(audioService.getCurrentPlaylistInfo());
    });

    // Initialize current state
    setCurrentlyPlayingTrack(audioService.getCurrentTrack());
    setIsMainPlayerPlaying(audioService.getIsPlaying());

    return () => {
      unsubscribe();
    };
  }, [loadData]);

  const handleCreatePlaylist = async () => {
    if (!newPlaylistName.trim()) return;

    try {
      await playlistService.create({
        name: newPlaylistName.trim(),
        storeId: user?.storeId,
        tracks: []
      });
      setNewPlaylistName('');
      setShowCreateDialog(false);
      loadData();
    } catch (error) {
      console.error('Failed to create playlist:', error);
    }
  };

  const handleDeletePlaylist = async (playlistId) => {
    try {
      await playlistService.delete(playlistId);
      if (selectedPlaylist?._id === playlistId) {
        setSelectedPlaylist(null);
      }
      loadData();
    } catch (error) {
      console.error('Failed to delete playlist:', error);
    }
  };

  const handleAddTrackToPlaylist = async (trackId) => {
    if (!selectedPlaylist) return;

    // Get current track IDs from the playlist
    const currentTrackIds = selectedPlaylist.tracks ?
      selectedPlaylist.tracks.map(track => track._id || track) :
      [];

    // Check if track is already in playlist
    if (currentTrackIds.includes(trackId)) {
      console.log('Track already in playlist');
      return;
    }

    const updatedTracks = [...currentTrackIds, trackId];

    try {
      await playlistService.update(selectedPlaylist._id, {
        trackIds: updatedTracks
      });

      // Reload data to get updated playlist with populated tracks
      await loadData();

      // Update selected playlist with fresh data
      const updatedPlaylists = await playlistService.getByStore(user?.storeId);
      const playlistData = updatedPlaylists.data?.data || updatedPlaylists.data || [];
      const updatedPlaylist = playlistData.find(p => p._id === selectedPlaylist._id);
      if (updatedPlaylist) {
        setSelectedPlaylist(updatedPlaylist);
      }
    } catch (error) {
      console.error('Failed to add track to playlist:', error);
    }
  };

  const handleRemoveTrackFromPlaylist = async (trackId) => {
    if (!selectedPlaylist) return;

    // Get current track IDs from the playlist
    const currentTrackIds = selectedPlaylist.tracks ?
      selectedPlaylist.tracks.map(track => track._id || track) :
      [];

    const updatedTracks = currentTrackIds.filter(id => id !== trackId);

    try {
      await playlistService.update(selectedPlaylist._id, {
        trackIds: updatedTracks
      });

      // Reload data to get updated playlist with populated tracks
      await loadData();

      // Update selected playlist with fresh data
      const updatedPlaylists = await playlistService.getByStore(user?.storeId);
      const playlistData = updatedPlaylists.data?.data || updatedPlaylists.data || [];
      const updatedPlaylist = playlistData.find(p => p._id === selectedPlaylist._id);
      if (updatedPlaylist) {
        setSelectedPlaylist(updatedPlaylist);
      }
    } catch (error) {
      console.error('Failed to remove track from playlist:', error);
    }
  };

  const handlePreviewTrack = (track) => {
    togglePreview(track);
  };

  const handlePlayPlaylist = async (playlist) => {
    try {
      // If playlist has tracks, play them
      if (playlist.tracks && playlist.tracks.length > 0) {
        // Use MediaContext's playMusic function to properly integrate with the persistent player
        // Pass playlist metadata for proper tracking and display
        const playlistInfo = {
          _id: playlist._id,
          name: playlist.name
        };
        playMusic(playlist.tracks, 0, playlistInfo);
        console.log(`Playing playlist: ${playlist.name} with ${playlist.tracks.length} tracks`);
      } else {
        console.warn('Playlist has no tracks to play');
        // You could show a toast notification here
      }
    } catch (error) {
      console.error('Failed to play playlist:', error);
      // You could show an error toast notification here
    }
  };

  // Filter playlists - ensure playlists is always an array
  const filteredPlaylists = (Array.isArray(playlists) ? playlists : []).filter(playlist => {
    if (playlistFilter === 'all') return true;
    if (playlistFilter === 'empty') return !playlist.tracks || playlist.tracks.length === 0;
    if (playlistFilter === 'populated') return playlist.tracks && playlist.tracks.length > 0;
    return true;
  });

  // Filter tracks - ensure tracks is always an array
  const filteredTracks = (Array.isArray(tracks) ? tracks : []).filter(track => {
    const matchesSearch = track.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      track.artist?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      track.album?.toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    if (trackFilter === 'all') return true;
    if (trackFilter === 'rock') return track.genre?.toLowerCase().includes('rock');
    if (trackFilter === 'pop') return track.genre?.toLowerCase().includes('pop');
    if (trackFilter === 'jazz') return track.genre?.toLowerCase().includes('jazz');
    if (trackFilter === 'classical') return track.genre?.toLowerCase().includes('classical');
    if (trackFilter === 'electronic') return track.genre?.toLowerCase().includes('electronic');
    return true;
  });

  // Pagination
  const totalPlaylistPages = Math.ceil(filteredPlaylists.length / playlistsPerPage);
  const totalTrackPages = Math.ceil(filteredTracks.length / tracksPerPage);

  const paginatedPlaylists = filteredPlaylists.slice(
    (playlistPage - 1) * playlistsPerPage,
    playlistPage * playlistsPerPage
  );

  const paginatedTracks = filteredTracks.slice(
    (trackPage - 1) * tracksPerPage,
    trackPage * tracksPerPage
  );

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Helper function to check if a playlist is currently playing
  const isPlaylistCurrentlyPlaying = (playlist) => {
    return currentPlaylistInfo?.id === playlist._id && isMainPlayerPlaying;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{
      p: 3,
      '& @keyframes pulse': {
        '0%': { opacity: 1 },
        '50%': { opacity: 0.5 },
        '100%': { opacity: 1 }
      }
    }}>
      {/* Header with responsive layout */}
      <Box sx={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: isMobile ? 'stretch' : 'center',
        mb: 3,
        gap: isMobile ? 2 : 0
      }}>
        <Typography variant={isMobile ? "h5" : "h4"} sx={{ fontWeight: 700 }}>
          Playlist Manager
        </Typography>

        {/* Mobile filters toggle */}
        {isMobile && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => setShowMobileFilters(!showMobileFilters)}
              size="small"
              fullWidth
            >
              Filters
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setShowCreateDialog(true)}
              size="small"
              fullWidth
            >
              Create
            </Button>
          </Box>
        )}

        {/* Desktop create button */}
        {!isMobile && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setShowCreateDialog(true)}
            sx={{ borderRadius: 2 }}
          >
            Create Playlist
          </Button>
        )}
      </Box>

      {/* Mobile filters collapse */}
      {isMobile && (
        <Collapse in={showMobileFilters}>
          <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Playlist Filter</InputLabel>
                  <Select
                    value={playlistFilter}
                    label="Playlist Filter"
                    onChange={(e) => setPlaylistFilter(e.target.value)}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <MenuItem value="empty">Empty</MenuItem>
                    <MenuItem value="populated">With Tracks</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Track Filter</InputLabel>
                  <Select
                    value={trackFilter}
                    label="Track Filter"
                    onChange={(e) => setTrackFilter(e.target.value)}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <MenuItem value="available">Available</MenuItem>
                    <MenuItem value="in_playlists">In Playlists</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Paper>
        </Collapse>
      )}

      <Grid container spacing={isMobile ? 2 : 3}>
        {/* Playlists Section */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: isMobile ? 2 : 3, height: 'fit-content', borderRadius: 3 }}>
            {/* Mobile collapsible header */}
            {isMobile ? (
              <Box sx={{ mb: 2 }}>
                <Button
                  fullWidth
                  onClick={() => setMobilePlaylistsExpanded(!mobilePlaylistsExpanded)}
                  sx={{
                    justifyContent: 'space-between',
                    textTransform: 'none',
                    color: 'text.primary',
                    fontWeight: 600
                  }}
                  endIcon={mobilePlaylistsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                >
                  Your Playlists ({filteredPlaylists.length})
                </Button>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Your Playlists
                </Typography>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Filter</InputLabel>
                  <Select
                    value={playlistFilter}
                    label="Filter"
                    onChange={(e) => setPlaylistFilter(e.target.value)}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <MenuItem value="empty">Empty</MenuItem>
                    <MenuItem value="populated">With Tracks</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            )}

            <Collapse in={!isMobile || mobilePlaylistsExpanded}>
              {filteredPlaylists.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {playlists.length === 0 ? 'No playlists yet. Create your first playlist!' : 'No playlists match the current filter.'}
                  </Typography>
                </Box>
              ) : (
              <List sx={{ p: 0 }}>
                {paginatedPlaylists.map((playlist) => {
                  const isCurrentlyPlaying = isPlaylistCurrentlyPlaying(playlist);
                  return (
                    <ListItem
                      key={playlist._id}
                      button
                      selected={selectedPlaylist?._id === playlist._id}
                      onClick={() => setSelectedPlaylist(playlist)}
                      sx={{
                        borderRadius: 2,
                        mb: 1,
                        border: isCurrentlyPlaying ? '2px solid' : 'none',
                        borderColor: isCurrentlyPlaying ? 'success.main' : 'transparent',
                        bgcolor: isCurrentlyPlaying ? 'success.50' : 'transparent',
                        '&.Mui-selected': {
                          bgcolor: selectedPlaylist?._id === playlist._id && !isCurrentlyPlaying ? 'primary.50' : isCurrentlyPlaying ? 'success.50' : 'primary.50',
                          '&:hover': {
                            bgcolor: selectedPlaylist?._id === playlist._id && !isCurrentlyPlaying ? 'primary.100' : isCurrentlyPlaying ? 'success.100' : 'primary.100'
                          }
                        },
                        '&:hover': {
                          bgcolor: isCurrentlyPlaying ? 'success.100' : 'action.hover'
                        }
                      }}
                    >
                      <ListItemIcon>
                        <MusicNoteIcon
                          color={
                            isCurrentlyPlaying ? 'success' :
                            selectedPlaylist?._id === playlist._id ? 'primary' : 'inherit'
                          }
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body1">
                              {playlist.name}
                            </Typography>
                            {isCurrentlyPlaying && (
                              <Chip
                                label="Now Playing"
                                size="small"
                                color="success"
                                sx={{
                                  height: 20,
                                  fontSize: '0.7rem',
                                  animation: 'pulse 2s infinite'
                                }}
                              />
                            )}
                          </Box>
                        }
                        secondary={`${playlist.tracks?.length || 0} tracks`}
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title={isCurrentlyPlaying ? "Pause Playlist" : "Play Playlist"}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (isCurrentlyPlaying) {
                                  // Pause the current playlist
                                  audioService.pause();
                                } else {
                                  // Play the playlist
                                  handlePlayPlaylist(playlist);
                                }
                              }}
                              disabled={!playlist.tracks || playlist.tracks.length === 0}
                              sx={{
                                color: isCurrentlyPlaying ? 'warning.main' : 'success.main',
                                '&:disabled': { color: 'text.disabled' }
                              }}
                            >
                              {isCurrentlyPlaying ? <PauseIcon fontSize="small" /> : <PlayIcon fontSize="small" />}
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Playlist">
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeletePlaylist(playlist._id);
                              }}
                              sx={{ color: 'error.main' }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
              )}
            </Collapse>
          </Paper>

          {/* Selected Playlist Tracks */}
          {selectedPlaylist && (
            <Paper sx={{ p: 3, mt: 3, borderRadius: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                {selectedPlaylist.name} Tracks
              </Typography>

              {selectedPlaylist.tracks?.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    No tracks in this playlist. Add some tracks from the library!
                  </Typography>
                </Box>
              ) : (
                <>
                  <List sx={{ p: 0, maxHeight: 300, overflow: 'auto' }}>
                    {selectedPlaylist.tracks?.map((track, index) => {
                      const isCurrentlyPlaying = currentlyPlayingTrack?._id === track._id && isMainPlayerPlaying;
                      return (
                        <ListItem
                          key={track._id}
                          sx={{
                            px: 0,
                            py: 1,
                            bgcolor: isCurrentlyPlaying ? 'primary.50' : 'transparent',
                            borderRadius: 1,
                            border: isCurrentlyPlaying ? '1px solid' : 'none',
                            borderColor: isCurrentlyPlaying ? 'primary.main' : 'transparent'
                          }}
                        >
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            {isCurrentlyPlaying ? (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Box
                                  sx={{
                                    width: 8,
                                    height: 8,
                                    bgcolor: 'primary.main',
                                    borderRadius: '50%',
                                    animation: 'pulse 2s infinite',
                                    mr: 1
                                  }}
                                />
                                <Typography variant="caption" sx={{ color: 'primary.main', fontWeight: 600 }}>
                                  {index + 1}
                                </Typography>
                              </Box>
                            ) : (
                              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                {index + 1}
                              </Typography>
                            )}
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: isCurrentlyPlaying ? 600 : 400,
                                  color: isCurrentlyPlaying ? 'primary.main' : 'text.primary'
                                }}
                              >
                                {track.title}
                              </Typography>
                            }
                            secondary={
                              <Typography
                                variant="caption"
                                sx={{
                                  color: isCurrentlyPlaying ? 'primary.dark' : 'text.secondary'
                                }}
                              >
                                {track.artist}
                              </Typography>
                            }
                            sx={{ mr: 1 }}
                          />
                          {isCurrentlyPlaying && (
                            <Box sx={{ mr: 1 }}>
                              <Chip
                                label="Playing"
                                size="small"
                                color="primary"
                                variant="outlined"
                                sx={{ fontSize: '0.7rem', height: 20 }}
                              />
                            </Box>
                          )}
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveTrackFromPlaylist(track._id)}
                            sx={{ color: 'error.main' }}
                          >
                            <CloseIcon fontSize="small" />
                          </IconButton>
                        </ListItem>
                      );
                    })}
                  </List>

                  {/* Playlist Pagination */}
                  {totalPlaylistPages > 1 && (
                    <Stack spacing={2} alignItems="center" sx={{ mt: 2 }}>
                      <Pagination
                        count={totalPlaylistPages}
                        page={playlistPage}
                        onChange={(_, newPage) => setPlaylistPage(newPage)}
                        color="primary"
                        size="small"
                      />
                    </Stack>
                  )}
                </>
              )}
            </Paper>
          )}
        </Grid>

        {/* Track Library Section */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: isMobile ? 2 : 3, borderRadius: 3 }}>
            {/* Mobile collapsible header */}
            {isMobile ? (
              <Box sx={{ mb: 2 }}>
                <Button
                  fullWidth
                  onClick={() => setMobileTracksExpanded(!mobileTracksExpanded)}
                  sx={{
                    justifyContent: 'space-between',
                    textTransform: 'none',
                    color: 'text.primary',
                    fontWeight: 600
                  }}
                  endIcon={mobileTracksExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                >
                  Track Library ({paginatedTracks.length})
                </Button>
              </Box>
            ) : (
              <Box sx={{
                display: 'flex',
                flexDirection: isMobile ? 'column' : 'row',
                justifyContent: 'space-between',
                alignItems: isMobile ? 'stretch' : 'center',
                mb: 3,
                gap: isMobile ? 2 : 0
              }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Track Library
                </Typography>
                <Box sx={{
                  display: 'flex',
                  flexDirection: isMobile ? 'column' : 'row',
                  gap: 2,
                  alignItems: isMobile ? 'stretch' : 'center'
                }}>
                  <FormControl size="small" sx={{ minWidth: isMobile ? 'auto' : 120 }}>
                    <InputLabel>Genre</InputLabel>
                    <Select
                      value={trackFilter}
                      label="Genre"
                      onChange={(e) => setTrackFilter(e.target.value)}
                    >
                      <MenuItem value="all">All Genres</MenuItem>
                      <MenuItem value="rock">Rock</MenuItem>
                      <MenuItem value="pop">Pop</MenuItem>
                      <MenuItem value="jazz">Jazz</MenuItem>
                      <MenuItem value="classical">Classical</MenuItem>
                      <MenuItem value="electronic">Electronic</MenuItem>
                    </Select>
                  </FormControl>
                  <TextField
                    size="small"
                    placeholder="Search tracks..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                    sx={{ width: isMobile ? 'auto' : 300 }}
                  />
                </Box>
              </Box>
            )}

            <Collapse in={!isMobile || mobileTracksExpanded}>
              <Box sx={{ minHeight: isMobile ? 200 : 400 }}>
                <Grid container spacing={isMobile ? 1 : 2}>
                  {paginatedTracks.map((track) => (
                    <Grid item xs={12} sm={6} lg={4} key={track._id}>
                    <Card sx={{
                      borderRadius: 2,
                      height: '100%',
                      ...(isMobile && {
                        '&:active': {
                          transform: 'scale(0.98)',
                          transition: 'transform 0.1s'
                        }
                      })
                    }}>
                      <CardContent sx={{ pb: 1, p: isMobile ? 1.5 : 2 }}>
                        <Typography
                          variant={isMobile ? "body1" : "subtitle1"}
                          sx={{
                            fontWeight: 600,
                            mb: 0.5,
                            fontSize: isMobile ? '0.9rem' : undefined
                          }}
                        >
                          {track.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'text.secondary',
                            mb: 1,
                            fontSize: isMobile ? '0.8rem' : undefined
                          }}
                        >
                          {track.artist}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color: 'text.secondary',
                            fontSize: isMobile ? '0.7rem' : undefined
                          }}
                        >
                          {track.album} • {formatDuration(track.duration)}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{
                        justifyContent: 'space-between',
                        px: isMobile ? 1.5 : 2,
                        pb: isMobile ? 1.5 : 2,
                        pt: 0
                      }}>
                        <Box>
                          <Tooltip title="Preview Track">
                            <IconButton
                              size={isMobile ? "medium" : "small"}
                              onClick={() => handlePreviewTrack(track)}
                              color={mediaState.previewState.currentTrack?._id === track._id && mediaState.previewState.isPlaying ? 'primary' : 'default'}
                              sx={{
                                ...(isMobile && {
                                  minWidth: 44,
                                  minHeight: 44
                                })
                              }}
                            >
                              {mediaState.previewState.currentTrack?._id === track._id && mediaState.previewState.isPlaying ? <PauseIcon /> : <PlayIcon />}
                            </IconButton>
                          </Tooltip>
                        </Box>
                        <Button
                          size={isMobile ? "medium" : "small"}
                          startIcon={!isMobile ? <PlaylistAddIcon /> : undefined}
                          onClick={() => handleAddTrackToPlaylist(track._id)}
                          disabled={!selectedPlaylist || (selectedPlaylist.tracks && selectedPlaylist.tracks.some(t => t._id === track._id))}
                          variant="outlined"
                          sx={{
                            ...(isMobile && {
                              minHeight: 36,
                              fontSize: '0.8rem'
                            })
                          }}
                        >
                          {isMobile ? (
                            selectedPlaylist && selectedPlaylist.tracks && selectedPlaylist.tracks.some(t => t._id === track._id) ? '✓' : '+'
                          ) : (
                            selectedPlaylist && selectedPlaylist.tracks && selectedPlaylist.tracks.some(t => t._id === track._id) ? 'Added' : 'Add'
                          )}
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Track Pagination */}
              {totalTrackPages > 1 && (
                <Stack spacing={2} alignItems="center" sx={{ mt: 3 }}>
                  <Pagination
                    count={totalTrackPages}
                    page={trackPage}
                    onChange={(_, newPage) => setTrackPage(newPage)}
                    color="primary"
                    size="small"
                  />
                </Stack>
              )}
              </Box>
            </Collapse>
          </Paper>
        </Grid>
      </Grid>

      {/* Create Playlist Dialog */}
      <Dialog open={showCreateDialog} onClose={() => setShowCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Playlist</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Playlist Name"
            fullWidth
            variant="outlined"
            value={newPlaylistName}
            onChange={(e) => setNewPlaylistName(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleCreatePlaylist();
              }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateDialog(false)}>Cancel</Button>
          <Button onClick={handleCreatePlaylist} variant="contained">
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Mobile FAB for creating playlists */}
      {isMobile && (
        <Fab
          color="primary"
          aria-label="create playlist"
          onClick={() => setShowCreateDialog(true)}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            zIndex: 1000
          }}
        >
          <AddIcon />
        </Fab>
      )}
    </Box>
  );
};

export default MaterialPlaylistManager;
