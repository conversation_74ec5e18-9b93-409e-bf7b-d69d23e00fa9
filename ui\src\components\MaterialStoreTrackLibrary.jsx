import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  Grid,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Fab,
  Pagination,
  Stack
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MusicNote as MusicIcon,
  AccessTime as TimeIcon,
  Album as AlbumIcon,
  Person as ArtistIcon,
  Add as AddIcon,
  Shuffle as ShuffleIcon,
  PlaylistPlay as PlaylistPlayIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useMedia } from '../context/MediaContext';
import { trackService, historyService } from '../services/api';

const MaterialStoreTrackLibrary = () => {
  const { user } = useAuth();
  const { mediaState, currentTrack, isPlaying, playMusic, pauseMusic, resumeMusic } = useMedia();

  // Extract playlist info from mediaState
  const playlist = mediaState?.musicState?.playlist || [];
  const currentIndex = mediaState?.musicState?.currentIndex || 0;
  const currentTrackFromState = mediaState?.musicState?.currentTrack;
  const isPlayingFromState = mediaState?.musicState?.isPlaying;
  
  const [tracks, setTracks] = useState([]);
  const [filteredTracks, setFilteredTracks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterGenre, setFilterGenre] = useState('');
  const [genres, setGenres] = useState([]);
  const [selectedTracks, setSelectedTracks] = useState([]);
  const [playingTrackId, setPlayingTrackId] = useState(null);

  // Pagination state
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTracks, setTotalTracks] = useState(0);
  const tracksPerPage = 20;

  useEffect(() => {
    loadTracks();
  }, [page, searchTerm, filterGenre]);

  useEffect(() => {
    // Reset to page 1 when search or filter changes
    if (page !== 1) {
      setPage(1);
    }
  }, [searchTerm, filterGenre]);

  // Update playing track ID when current track changes
  useEffect(() => {
    const activeTrack = currentTrackFromState || currentTrack;
    if (activeTrack?._id) {
      setPlayingTrackId(activeTrack._id);
    } else {
      setPlayingTrackId(null);
    }
  }, [currentTrackFromState, currentTrack]);

  const loadTracks = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: tracksPerPage,
        ...(searchTerm && { search: searchTerm }),
        ...(filterGenre && { genre: filterGenre })
      };

      const response = await trackService.getAll(params);
      const tracksData = Array.isArray(response.data?.data) ? response.data.data : [];
      const pagination = response.data?.pagination;

      setFilteredTracks(tracksData);
      setTotalPages(pagination?.pages || 1);
      setTotalTracks(pagination?.total || tracksData.length);

      // Load all tracks for genre extraction (only on first load)
      if (page === 1 && !searchTerm && !filterGenre) {
        const allTracksResponse = await trackService.getAll({ limit: 1000 });
        const allTracks = Array.isArray(allTracksResponse.data?.data) ? allTracksResponse.data.data : [];
        setTracks(allTracks);

        // Extract unique genres
        const uniqueGenres = [...new Set(allTracks.map(track => track.genre).filter(Boolean))];
        setGenres(uniqueGenres);
      }
    } catch (error) {
      console.error('Failed to load tracks:', error);
      setFilteredTracks([]);
      setTracks([]);
    } finally {
      setLoading(false);
    }
  };



  const handlePlayTrack = async (track) => {
    try {
      // Log manual track play for compliance
      await logManualTrackPlay(track);

      // Play the track
      const activeTrack = currentTrackFromState || currentTrack;
      const activeIsPlaying = isPlayingFromState !== undefined ? isPlayingFromState : isPlaying;

      if (activeTrack?._id === track._id && activeIsPlaying) {
        pauseMusic();
        setPlayingTrackId(null);
      } else if (activeTrack?._id === track._id && !activeIsPlaying) {
        resumeMusic();
        setPlayingTrackId(track._id);
      } else {
        // Create a playlist with the selected track and similar tracks for auto-play
        console.log('Loading similar tracks for:', track.title);
        const { similarTrackService } = await import('../services/similarTrackService');
        const similarTracks = await similarTrackService.findSimilarTracks(track, 10);
        const playlist = [track, ...similarTracks];

        console.log(`Created playlist with ${playlist.length} tracks (1 selected + ${similarTracks.length} similar)`);

        playMusic(playlist, 0, {
          name: `Playing: ${track.title}`,
          isManual: true,
          enableAutoPlay: true
        });
        setPlayingTrackId(track._id);
      }
    } catch (error) {
      console.error('Error playing track:', error);
    }
  };

  const logManualTrackPlay = async (track) => {
    try {
      const playData = {
        trackId: track._id,
        storeId: user?.storeId,
        playedDate: new Date(),
        startTime: new Date(),
        durationPlayed: 0, // Will be updated when track finishes
        completionPercentage: 0,
        playbackStatus: 'started',
        location: {
          country: 'ZA',
          city: 'Unknown',
          coordinates: { lat: 0, lng: 0 }
        },
        deviceId: 'store-dashboard',
        metadata: {
          sourceType: 'track_library',
          playlistId: null,
          playlistName: 'Manual Track Library Play',
          trackPosition: 0,
          repeatMode: false,
          shuffleMode: false
        },
        compliance: {
          reportedToSAMRO: false,
          reportedToSAMPRA: false,
          reportedToRISA: false
        },
        auditInfo: {
          sessionId: `manual-${Date.now()}`,
          playSequence: 1,
          isManualSelection: true,
          playbackSource: 'store_track_library',
          systemUsed: 'manual'
        }
      };

      await historyService.log(playData);
      console.log('Manual track play logged for compliance');
    } catch (error) {
      console.error('Failed to log manual track play:', error);
    }
  };

  const handlePlaySimilarTracks = async (track) => {
    try {
      // Find similar tracks based on genre and artist
      const similarTracks = tracks.filter(t => 
        t._id !== track._id && 
        (t.genre === track.genre || t.artist === track.artist)
      ).slice(0, 10);

      if (similarTracks.length > 0) {
        const playlist = [track, ...similarTracks];
        playMusic(playlist, 0, { 
          name: `Similar to: ${track.title}`, 
          isManual: true,
          autoPlaySimilar: true 
        });
        setPlayingTrackId(track._id);
      } else {
        handlePlayTrack(track);
      }
    } catch (error) {
      console.error('Error playing similar tracks:', error);
    }
  };

  const handleShufflePlay = () => {
    if (filteredTracks.length > 0) {
      const shuffled = [...filteredTracks].sort(() => Math.random() - 0.5);
      playMusic(shuffled, 0, { 
        name: 'Shuffled Track Library', 
        isManual: true,
        shuffleMode: true 
      });
      setPlayingTrackId(shuffled[0]._id);
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
          Track Library
        </Typography>
      </Box>

      {/* Search and Filter Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search tracks, artists, or albums..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Filter by Genre</InputLabel>
                <Select
                  value={filterGenre}
                  onChange={(e) => setFilterGenre(e.target.value)}
                  label="Filter by Genre"
                >
                  <MenuItem value="">All Genres</MenuItem>
                  {genres.map(genre => (
                    <MenuItem key={genre} value={genre}>{genre}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<ShuffleIcon />}
                onClick={handleShufflePlay}
                disabled={filteredTracks.length === 0}
              >
                Shuffle Play
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Stats */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <MusicIcon sx={{ fontSize: 40, color: '#1976d2', mb: 1 }} />
              <Typography variant="h6">{totalTracks}</Typography>
              <Typography variant="body2" color="text.secondary">
                {searchTerm || filterGenre ? 'Filtered Tracks' : 'Total Tracks'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AlbumIcon sx={{ fontSize: 40, color: '#1976d2', mb: 1 }} />
              <Typography variant="h6">{genres.length}</Typography>
              <Typography variant="body2" color="text.secondary">Genres</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ArtistIcon sx={{ fontSize: 40, color: '#1976d2', mb: 1 }} />
              <Typography variant="h6">
                {[...new Set(filteredTracks.map(t => t.artist))].length}
              </Typography>
              <Typography variant="body2" color="text.secondary">Artists</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TimeIcon sx={{ fontSize: 40, color: '#1976d2', mb: 1 }} />
              <Typography variant="h6">
                {Math.round(filteredTracks.reduce((acc, track) => acc + (track.duration || 0), 0) / 60)}m
              </Typography>
              <Typography variant="body2" color="text.secondary">Total Duration</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Currently Playing Section */}
      {(currentTrackFromState || currentTrack) && (
        <Card sx={{ mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <MusicIcon />
              Currently Playing
            </Typography>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={8}>
                <Typography variant="body1" fontWeight="bold">
                  {(currentTrackFromState || currentTrack).title}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  by {(currentTrackFromState || currentTrack).artist} {(currentTrackFromState || currentTrack).album && `• ${(currentTrackFromState || currentTrack).album}`}
                </Typography>
              </Grid>
              <Grid item xs={12} md={4} sx={{ textAlign: { xs: 'left', md: 'right' } }}>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
                  <Chip
                    label={(isPlayingFromState !== undefined ? isPlayingFromState : isPlaying) ? "Playing" : "Paused"}
                    color={(isPlayingFromState !== undefined ? isPlayingFromState : isPlaying) ? "success" : "warning"}
                  />
                  <Chip
                    label={(currentTrackFromState || currentTrack).genre || 'Unknown Genre'}
                    variant="outlined"
                    sx={{ bgcolor: 'rgba(255,255,255,0.1)', color: 'inherit' }}
                  />
                  {playlist && playlist.length > 1 && (
                    <Chip
                      label={`${(currentIndex || 0) + 1} of ${playlist.length}`}
                      variant="outlined"
                      sx={{ bgcolor: 'rgba(255,255,255,0.1)', color: 'inherit' }}
                    />
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}


      {/* Track List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Track Library ({filteredTracks.length} tracks)
          </Typography>

          {filteredTracks.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <MusicIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No tracks found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchTerm || filterGenre ? 'Try adjusting your search or filter criteria' : 'No tracks available in the library'}
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Actions</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Artist</TableCell>
                    <TableCell>Album</TableCell>
                    <TableCell>Genre</TableCell>
                    <TableCell>Duration</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTracks.map((track) => (
                    <TableRow
                      key={track._id}
                      sx={{
                        '&:hover': { backgroundColor: 'action.hover' },
                        backgroundColor: (currentTrackFromState || currentTrack)?._id === track._id ? 'action.selected' : 'inherit'
                      }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title={(currentTrackFromState || currentTrack)?._id === track._id && (isPlayingFromState !== undefined ? isPlayingFromState : isPlaying) ? 'Pause' : 'Play'}>
                            <IconButton
                              size="small"
                              onClick={() => handlePlayTrack(track)}
                              color={(currentTrackFromState || currentTrack)?._id === track._id ? 'primary' : 'default'}
                            >
                              {(currentTrackFromState || currentTrack)?._id === track._id && (isPlayingFromState !== undefined ? isPlayingFromState : isPlaying) ? <PauseIcon /> : <PlayIcon />}
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Play with similar tracks">
                            <IconButton
                              size="small"
                              onClick={() => handlePlaySimilarTracks(track)}
                              color="secondary"
                            >
                              <PlaylistPlayIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" fontWeight="medium">
                            {track.title}
                          </Typography>
                          {(currentTrackFromState || currentTrack)?._id === track._id && (
                            <Chip
                              label={(isPlayingFromState !== undefined ? isPlayingFromState : isPlaying) ? "Now Playing" : "Paused"}
                              size="small"
                              color={(isPlayingFromState !== undefined ? isPlayingFromState : isPlaying) ? "success" : "warning"}
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {track.artist}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {track.album || 'Unknown Album'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={track.genre || 'Unknown'}
                          size="small"
                          variant="outlined"
                          color="primary"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {formatDuration(track.duration || 0)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Stack spacing={2}>
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={(event, value) => setPage(value)}
                  color="primary"
                  size="large"
                  showFirstButton
                  showLastButton
                />
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                  Showing {((page - 1) * tracksPerPage) + 1}-{Math.min(page * tracksPerPage, totalTracks)} of {totalTracks} tracks
                </Typography>
              </Stack>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default MaterialStoreTrackLibrary;
