import { trackService, playlistService } from './api';

class SimilarTrackService {
  constructor() {
    this.allTracks = [];
    this.lastUpdated = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  async loadTracks() {
    // Check if we need to refresh the cache
    if (this.allTracks.length === 0 || 
        !this.lastUpdated || 
        Date.now() - this.lastUpdated > this.cacheTimeout) {
      
      try {
        const response = await trackService.getAll();
        this.allTracks = Array.isArray(response.data?.data) ? response.data.data : [];
        this.lastUpdated = Date.now();
        console.log(`Loaded ${this.allTracks.length} tracks for similarity matching`);
      } catch (error) {
        console.error('Failed to load tracks for similarity matching:', error);
        this.allTracks = [];
      }
    }
    return this.allTracks;
  }

  async findSimilarTracks(track, limit = 10) {
    await this.loadTracks();

    if (!track || this.allTracks.length === 0) {
      console.log('No track provided or no tracks available for similarity matching');
      return [];
    }

    console.log(`Finding similar tracks for: ${track.title} by ${track.artist} (${track.genre})`);
    console.log(`Available tracks for matching: ${this.allTracks.length}`);

    const similarTracks = [];
    const trackGenre = track.genre?.toLowerCase();
    const trackArtist = track.artist?.toLowerCase();
    
    // Score tracks based on similarity
    const scoredTracks = this.allTracks
      .filter(t => t._id !== track._id) // Exclude the current track
      .map(t => {
        let score = 0;
        
        // Genre match (highest priority)
        if (t.genre?.toLowerCase() === trackGenre) {
          score += 10;
        }
        
        // Same artist (high priority)
        if (t.artist?.toLowerCase() === trackArtist) {
          score += 8;
        }
        
        // Similar artist name (partial match)
        if (trackArtist && t.artist?.toLowerCase().includes(trackArtist)) {
          score += 5;
        }
        
        // Album match
        if (track.album && t.album?.toLowerCase() === track.album?.toLowerCase()) {
          score += 6;
        }
        
        // Duration similarity (within 30 seconds)
        if (track.duration && t.duration) {
          const durationDiff = Math.abs(track.duration - t.duration);
          if (durationDiff <= 30) {
            score += 3;
          } else if (durationDiff <= 60) {
            score += 1;
          }
        }
        
        // Genre similarity (partial match)
        if (trackGenre && t.genre?.toLowerCase().includes(trackGenre)) {
          score += 4;
        }
        
        return { track: t, score };
      })
      .filter(item => item.score > 0) // Only include tracks with some similarity
      .sort((a, b) => b.score - a.score) // Sort by score descending
      .slice(0, limit)
      .map(item => item.track);

    console.log(`Found ${scoredTracks.length} similar tracks for ${track.title}`);
    return scoredTracks;
  }

  async findSimilarByGenre(genre, excludeTrackId = null, limit = 20) {
    await this.loadTracks();
    
    if (!genre) {
      return [];
    }

    return this.allTracks
      .filter(track => 
        track.genre?.toLowerCase() === genre.toLowerCase() && 
        track._id !== excludeTrackId
      )
      .sort(() => Math.random() - 0.5) // Shuffle
      .slice(0, limit);
  }

  async findSimilarByArtist(artist, excludeTrackId = null, limit = 10) {
    await this.loadTracks();
    
    if (!artist) {
      return [];
    }

    return this.allTracks
      .filter(track => 
        track.artist?.toLowerCase() === artist.toLowerCase() && 
        track._id !== excludeTrackId
      )
      .slice(0, limit);
  }

  async createSimilarTrackPlaylist(track, includeOriginal = true) {
    const similarTracks = await this.findSimilarTracks(track, 15);
    
    if (similarTracks.length === 0) {
      // Fallback to genre-based similarity
      const genreTracks = await this.findSimilarByGenre(track.genre, track._id, 10);
      if (genreTracks.length === 0) {
        return includeOriginal ? [track] : [];
      }
      return includeOriginal ? [track, ...genreTracks] : genreTracks;
    }
    
    return includeOriginal ? [track, ...similarTracks] : similarTracks;
  }

  async shouldAutoPlaySimilar() {
    // Check if there are any scheduled playlists active
    try {
      // Import scheduleService dynamically to avoid circular dependency
      const { scheduleService } = await import('./scheduleService');

      // If schedule service is not running, allow auto-play
      if (!scheduleService.isRunning()) {
        console.log('Schedule service not running, allowing similar track auto-play');
        return true;
      }

      // Check if there are currently active scheduled playlists
      const hasActiveSchedule = await scheduleService.hasActiveScheduledPlaylist();

      if (hasActiveSchedule) {
        console.log('Active scheduled playlist found, disabling similar track auto-play');
        return false;
      }

      console.log('No active scheduled playlists, allowing similar track auto-play');
      return true;
    } catch (error) {
      console.error('Error checking scheduled playlists:', error);
      return true; // Default to allowing auto-play if check fails
    }
  }

  async getRecommendedTracks(userPreferences = {}, limit = 20) {
    await this.loadTracks();
    
    const { favoriteGenres = [], favoriteArtists = [], recentlyPlayed = [] } = userPreferences;
    
    let recommendedTracks = [];
    
    // Add tracks from favorite genres
    for (const genre of favoriteGenres) {
      const genreTracks = await this.findSimilarByGenre(genre, null, 5);
      recommendedTracks.push(...genreTracks);
    }
    
    // Add tracks from favorite artists
    for (const artist of favoriteArtists) {
      const artistTracks = await this.findSimilarByArtist(artist, null, 3);
      recommendedTracks.push(...artistTracks);
    }
    
    // Remove duplicates and recently played tracks
    const uniqueTracks = recommendedTracks.filter((track, index, self) => 
      index === self.findIndex(t => t._id === track._id) &&
      !recentlyPlayed.some(recentTrack => recentTrack._id === track._id)
    );
    
    // If we don't have enough recommendations, add random tracks
    if (uniqueTracks.length < limit) {
      const randomTracks = this.allTracks
        .filter(track => !uniqueTracks.some(ut => ut._id === track._id))
        .sort(() => Math.random() - 0.5)
        .slice(0, limit - uniqueTracks.length);
      
      uniqueTracks.push(...randomTracks);
    }
    
    return uniqueTracks.slice(0, limit);
  }

  // Method to analyze user listening patterns
  analyzeListeningPatterns(playHistory) {
    const patterns = {
      favoriteGenres: {},
      favoriteArtists: {},
      listeningTimes: {},
      averageTrackDuration: 0
    };

    if (!Array.isArray(playHistory) || playHistory.length === 0) {
      return patterns;
    }

    let totalDuration = 0;
    let trackCount = 0;

    playHistory.forEach(play => {
      // Count genres
      if (play.genre) {
        patterns.favoriteGenres[play.genre] = (patterns.favoriteGenres[play.genre] || 0) + 1;
      }

      // Count artists
      if (play.artist) {
        patterns.favoriteArtists[play.artist] = (patterns.favoriteArtists[play.artist] || 0) + 1;
      }

      // Analyze listening times
      if (play.playedDate) {
        const hour = new Date(play.playedDate).getHours();
        patterns.listeningTimes[hour] = (patterns.listeningTimes[hour] || 0) + 1;
      }

      // Track duration
      if (play.durationPlayed) {
        totalDuration += play.durationPlayed;
        trackCount++;
      }
    });

    // Calculate average track duration
    patterns.averageTrackDuration = trackCount > 0 ? totalDuration / trackCount : 0;

    // Sort favorites by count
    patterns.favoriteGenres = Object.entries(patterns.favoriteGenres)
      .sort(([,a], [,b]) => b - a)
      .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});

    patterns.favoriteArtists = Object.entries(patterns.favoriteArtists)
      .sort(([,a], [,b]) => b - a)
      .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});

    return patterns;
  }

  // Clear cache when needed
  clearCache() {
    this.allTracks = [];
    this.lastUpdated = null;
  }
}

export const similarTrackService = new SimilarTrackService();
export default similarTrackService;
